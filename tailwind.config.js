/** @type {import('tailwindcss').Config} */
const config = {
  /* eslint-disable @typescript-eslint/no-var-requires */
  presets: [require('./tailwindVentureCPreset.config')],
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: 'hsl(var(--primary)/<alpha-value>)',
        secondary: 'var(--secondary)',
        black: 'hsl(var(--black)/<alpha-value>)',
        blackFive: 'var(--blackFive)',
        subText: 'var(--subText)',
        white: 'var(--white)',
        'white-opaque': 'hsl(var(--white-opaque)/<alpha-value>)',
        whiteOff: 'var(--whiteOff)',
        green: 'var(--green)',
        greenOne: 'var(--greenOne)',
        greenFade: 'var(--greenFade)',
        orangeTwo: 'hsl(var(--orangeTwo)/<alpha-value>)',
        orangeTwoFade: 'var(--orangeTwoFade)',
        orangeThreeFade: 'var(--orangeThreeFade)',
        orangeFour: 'var(--orangeFour)',
        orangeThree: 'var(--orangeThree)',
        orangeFourFade: 'var(--orangeFourFade)',
        peachOne: 'var(--peachOne)',
        purple: 'var(--purple)',
        purpleFade: 'var(--purpleFade)',
        purpleTwoFade: 'var(--purpleTwoFade)',
        yellowFade: 'var(--yellowFade)',
        yellow: 'var(--yellow)',
        graySeven: 'var(--graySeven)',
        grayNine: 'var(--grayNine)',
        grayTen: 'var(--grayTen)',
        grayEleven: 'var(--grayEleven)',
        grayTwelve: 'var(--grayTwelve)',
        grayThirteen: 'var(--grayThirteen)',
        grayFourteen: 'var(--grayFourteen)',
        grayFifteen: 'var(--grayFifteen)',
        graySixteen: 'var(--graySixteen)',
        graySeventeen: 'var(--graySeventeen)',
        grayEigtheen: 'var(--grayEigtheen)',
        grayNineTeen: 'var(--grayNineTeen)',
        grayTwenty: 'var(--grayTwenty)',
        grayTwentyOne: 'var(--grayTwentyOne)',
        grayTwentyTwo: 'var(--grayTwentyTwo)',
        grayTwo: 'var(--grayTwo)',
        grayOne: 'var(--grayOne)',
        lightBrown: 'var(--lightBrown)',
        lightPurple: 'var(--lightPurple)',
        lightGreen: 'var(--lightGreen)',
        greenTwo: 'var(--greenTwo)',
        brown: 'var(--brown)',
        lightOrangeTwo: 'var(--light-orangeTwo)',
        dangerTwo: 'var(--dangerTwo)',
        successTwo: 'var(--successTwo)',
        lightYellow: 'var(--lightYellow)',
        lightYellowTwo: 'var(--lightYellowTwo)',
        blue: {
          midnight: 'var(--midnight-blue)',
        },
        blueGray: 'var(--blueGray)',
        darkGray: 'var(--dark-gray)',
        darkBlue: 'var(--darkBlue)',
        linkLightBlue: 'var(--linkLightBlue)',
        orange: {
          5: 'var(--light-orange)',
          10: 'var(--orange)',
          15: 'var(--dark-orange)',
        },
        peach: {
          5: 'var(--light-peach)',
        },
        success: 'var(--success)',
        danger: 'var(--danger)',
        warning: 'var(--warning)',
        disabled: 'var(--disabled)',
        alert: 'var(--alert)',
      },
      fontFamily: {
        spartan: ["'Spartan'", 'sans-serif'],
      },
    },
  },
  /* eslint-disable @typescript-eslint/no-var-requires */
  plugins: [require('tailwind-scrollbar')({ nocompatible: true })],
};

export default config;
