import { useKeycloak } from '@react-keycloak/web';
import { useEffect, useState } from 'react';
import { io as ClientIO, Socket } from 'socket.io-client';

import { SOCKET_URL } from '../../utils/apiUrls';

export default function useSocketContextStateAndActions() {
  const { keycloak } = useKeycloak();
  const [socket, setSocket] = useState<Socket<any, any> | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // if (!keycloak?.token) {
    //   return;
    // }
    const socketInstance: Socket<any, any> = ClientIO(SOCKET_URL!, {
      query: {
        accessToken:
          keycloak?.token ||
          'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJxUS0xa1FXOW1mZ2thUEh2RFJkdTViaXkwUG82aEVWZGtfazRkOHFPbGJZIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XR-6NO91GZzfptZiKdReeh9gtAsqcTKdHh4FjOcvlrKMN13i4uyANOWKe9QERLMCqN8X6xGQz7eb-qLsFXu2Oju0iHdAeO4-o0nHXSnCDkTxHvlQIm_RmBqry5BtV3I0AyGnYpzLwjGVPz2aoEJso_iP7LXRI9FPhsYZWAUOIE7c8meiYViJHNwYqEDD5kSDF2mrdG9CsHM3XlfLdjUU5mBEoRoi80BHZy2EHDnEXIl9LilUQXGaQ_1Qh8uRaomGJ18TTqRlng0rgCvbBzpq0MfVYpDgY36_YQ8_c23sZRzq6KQUkyNZBbC18iw_avKETJ_seRvUBn9FJUS7W2FHow',
      },
      addTrailingSlash: false,
      timeout: 120000,
    });
    if (!socketInstance) {
      return;
    }

    socketInstance.on('connect', () => {
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      setIsConnected(false);
    });

    window.addEventListener('focus', () => {
      socketInstance.emit('CONNECTED', () => {
        setIsConnected(true);
      });
    });

    window.addEventListener('blur', () => {
      socketInstance.emit('DISCONNECTED', () => {
        setIsConnected(false);
      });
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
      window.removeEventListener('focus', () =>
        socketInstance.emit('CONNECTED', () => {
          setIsConnected(true);
        }),
      );
      window.removeEventListener('blur', () =>
        socketInstance.emit('DISCONNECTED', () => {
          setIsConnected(false);
        }),
      );
    };
  }, [setIsConnected]);

  return {
    socket,
    isConnected,
  };
}
