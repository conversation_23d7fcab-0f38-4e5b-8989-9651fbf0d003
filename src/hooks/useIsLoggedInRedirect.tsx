import { useAppContext } from '@/context/event/AppEventContext';
import { useUserContext } from '@/context/user/UserContext';
import { isDevEnvironment } from '@/features/UserOnboarding/utils/helper';

export function useIsLoggedInRedirect() {
  const { isLoggedIn } = useUserContext();
  const { setShowModalHandler } = useAppContext();

  const isLoggedInRedirect = (action: () => void) => {
    if (!isLoggedIn && !isDevEnvironment()) {
      setShowModalHandler('AuthenticationPromptModal');
    } else {
      action();
    }
  };
  return { isLoggedInRedirect };
}
