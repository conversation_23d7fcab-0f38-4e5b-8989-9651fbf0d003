import { useAppContext } from '@/context/event/AppEventContext';
import { useUserContext } from '@/context/user/UserContext';

export function useIsLoggedInRedirect() {
  const { isLoggedIn } = useUserContext();
  const { setShowModalHandler } = useAppContext();

  const isLoggedInRedirect = (action: () => void) => {
    if (!isLoggedIn) {
      setShowModalHandler('AuthenticationPromptModal');
    } else {
      action();
    }
  };
  return { isLoggedInRedirect };
}
