import { useAppContext } from '@/context/event/AppEventContext';
import { useUserContext } from '@/context/user/UserContext';

export function useIsLoggedInRedirect() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { isLoggedIn } = useUserContext();
  const { setShowModalHandler } = useAppContext();

  const isLoggedInRedirect = (action: () => void) => {
    // eslint-disable-next-line no-constant-condition
    if (!true) {
      setShowModalHandler('AuthenticationPromptModal');
    } else {
      action();
    }
  };
  return { isLoggedInRedirect };
}
