import { useKeycloak } from '@react-keycloak/web';
import { useMutation, useQuery } from '@tanstack/react-query';

import {
  useGetCountriesApi,
  useGetInstitutionsApi,
  useGetUniversitiesApi,
  useGetUniversitiesByCountryCodeApi,
  useGetUserApi,
  useGetUserByUserIdApi,
  useVerifyEmailSuffixApi,
  useVerifyEmailSuffixForPractitionerApi,
} from '../../services/userApiRequests';
import { ServerUserBasicInfoPayload } from '../../types';
import {
  GET_USER_BY_USERID_QUERY,
  GET_USER_QUERY,
} from '../../utils/queryKeys';

export const useGetUser = <T extends ServerUserBasicInfoPayload>(
  options = {},
) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { initialized, keycloak } = useKeycloak();
  const getUser = useGetUserApi();
  return useQuery([GET_USER_QUERY], () => getUser<T>(), {
    ...options,
    select: data => {
      const {
        user: { dateOfBirth, ...userRest },
        ...others
      } = data;
      const newData = {
        ...others,
        user: {
          ...userRest,
          dateOfBirth: dateOfBirth && new Date(dateOfBirth),
        },
      };
      return newData;
    },
    // enabled: !!initialized && !!keycloak.authenticated,
  });
};
export const useGetUserByUserId = (params: { id: string }, options = {}) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { initialized, keycloak } = useKeycloak();
  const getUserByUserId = useGetUserByUserIdApi();
  return useQuery(
    [GET_USER_BY_USERID_QUERY, params.id],
    () => getUserByUserId(params.id),
    {
      ...options,
      select: data => {
        const {
          user: { dateOfBirth, ...userRest },
          ...others
        } = data;
        const newData = {
          ...others,
          user: {
            ...userRest,
            dateOfBirth: dateOfBirth && new Date(dateOfBirth),
          },
        };
        return newData;
      },
      // enabled: !!initialized && !!keycloak.authenticated && !!params.id,
    },
  );
};

export const useGetCountries = (options = {}) => {
  const getCountries = useGetCountriesApi();
  return useQuery(['GET_COUNTRIES_QUERY'], () => getCountries(), {
    ...options,
  });
};
export const useGetUniversitiesByCountryCode = (
  countryCode: string,
  options = {},
) => {
  const getUniversitiesByCountryCode = useGetUniversitiesByCountryCodeApi();
  return useQuery(
    ['GET_UNIVERSITY_QUERY', countryCode],
    () => getUniversitiesByCountryCode(countryCode),
    {
      ...options,
    },
  );
};
export const useVerifyEmailSuffix = <T>(options = {}) => {
  const verifyEmailSuffix = useVerifyEmailSuffixApi();
  return useMutation<any, any, T, any>(verifyEmailSuffix, {
    mutationKey: ['VERIFY_EMAIL_SUFFIX_QUERY '],
    ...options,
  });
};
export const useVerifyEmailSuffixForPractitioner = <T>(options = {}) => {
  const caller = useVerifyEmailSuffixForPractitionerApi();
  return useMutation<any, any, T, any>(caller, {
    mutationKey: ['VERIFY_INSTITUTION_EMAIL_SUFFIX_QUERY '],
    ...options,
  });
};

export const useGetUniversities = (options = {}) => {
  const getUniversities = useGetUniversitiesApi();
  return useQuery(['GET_UNIVERSITIES_QUERY'], () => getUniversities(), {
    ...options,
  });
};
export const useGetInstitutions = (options = {}) => {
  const caller = useGetInstitutionsApi();
  return useQuery(['GET_INSTITUTIONS_QUERY'], () => caller(), {
    ...options,
  });
};
