import { lazy } from 'react';
import { withSuspense } from '../../components/hocs/suspense/withSuspense';

const LaunchPage = withSuspense(lazy(() => import('../../pages/LaunchPage')));
const ProblemStatementOptionsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProblemStatementOptionsPage')),
);
const CreateProblemStatementPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/CreateProblemStatementPage')),
);
const ProposedSolutionsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProposedSolutionsPage')),
);
const ProposedSolutionDetailsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProposedSolutionDetailsPage')),
);
const ProposedProjectsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProposedProjectsPage')),
);
const ProjectDetailsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProjectDetailsPage')),
);

export const launchChildren = [
  {
    index: true,
    element: <LaunchPage />,
  },
  {
    path: 'problem-statement',
    element: <ProblemStatementOptionsPage />,
  },
  {
    path: 'proposed-solutions',
    element: <ProposedSolutionsPage />,
  },
  {
    path: 'proposed-solutions/:solutionRef',
    element: <ProposedSolutionDetailsPage />,
  },
  {
    path: 'proposed-projects',
    element: <ProposedProjectsPage />,
  },
  {
    path: 'proposed-projects/:projectRef',
    element: <ProjectDetailsPage />,
  },
  {
    path: 'problem-statement/create',
    element: <CreateProblemStatementPage />,
  },
];
