import { lazy } from 'react';
import { withSuspense } from '../../components/hocs/suspense/withSuspense';

const LaunchPage = withSuspense(lazy(() => import('../../pages/LaunchPage')));
const ProblemStatementOptionsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProblemStatementOptionsPage')),
);
const CreateProblemStatementPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/CreateProblemStatementPage')),
);

export const launchChildren = [
  {
    index: true,
    element: <LaunchPage />,
  },
  {
    path: 'problem-statement',
    element: <ProblemStatementOptionsPage />,
  },
  {
    path: 'problem-statement/create',
    element: <CreateProblemStatementPage />,
  },
];
