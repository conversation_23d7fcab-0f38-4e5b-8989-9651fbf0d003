import { lazy } from 'react';
import { withSuspense } from '../../components/hocs/suspense/withSuspense';

const LaunchPage = withSuspense(lazy(() => import('../../pages/LaunchPage')));
const ProblemStatementOptionsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProblemStatementOptionsPage')),
);
const CreateProblemStatementPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/CreateProblemStatementPage')),
);
const ProposedSolutionsPage = withSuspense(
  lazy(() => import('../../pages/LaunchPage/ProposedSolutionsPage')),
);

export const launchChildren = [
  {
    index: true,
    element: <LaunchPage />,
  },
  {
    path: 'problem-statement',
    element: <ProblemStatementOptionsPage />,
  },
  {
    path: 'proposed-solutions',
    element: <ProposedSolutionsPage />,
  },
  {
    path: 'problem-statement/create',
    element: <CreateProblemStatementPage />,
  },
];
