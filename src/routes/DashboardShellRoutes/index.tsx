import { lazy } from 'react';

import { withSuspense } from '../../components/hocs/suspense/withSuspense';
import { profileChildren } from './profileChildren';
import { projectsChildren } from './projectsChildren';
import { statementsChildren } from './statementsChildren';
import { launchChildren } from './launchChildren';
import { teamsChildren } from './teamsChildren';
import { subscriptionChildren } from './subscriptionChildren';
import { grantsChildren } from './grantsChildren';
import { applicationsChildren } from './applicationsChildren';
import ChatPage from '@/pages/ChatPage';
import { talentChildren } from './talentChildren';
import { usersChildren } from './usersChildren';
import { channelsChildren } from './channelsChildren';
import { mentorChildren } from './mentorChildren';
import ChannelChatListPage from '@/pages/ConversationPage/ChannelChatListPage';
import ChannelChatPage from '@/pages/ConversationPage/ChannelChatListPage/ChannelChatPage';

const DashboardOverviewPage = withSuspense(
  lazy(() => import('../../pages/DashboardOverviewPage')),
);

const GoalsPage = withSuspense(lazy(() => import('../../pages/GoalsPage')));

const DashboardCoursesPage = withSuspense(
  lazy(() => import('../../pages/DashboardCoursesPage')),
);

const FeedbackAndSupportPage = withSuspense(
  lazy(() => import('../../pages/FeedbackAndSupportPage')),
);
const DashboardWatchPage = withSuspense(
  lazy(() => import('../../pages/DashboardWatchPage')),
);
const NotificationPage = withSuspense(
  lazy(() => import('../../pages/NotificationPage')),
);
const DashboardBooksPage = withSuspense(
  lazy(() => import('../../pages/DashboardBooksPage')),
);
const DashboardReadPage = withSuspense(
  lazy(() => import('../../pages/DashboardReadPage')),
);
const FeedPage = withSuspense(
  lazy(() => import('../../pages/ConversationPage')),
);
const TopicModalForTopicIdParamsPage = withSuspense(
  lazy(
    () => import('../../pages/ConversationPage/TopicModalForTopicIdParamsPage'),
  ),
);
const ProposedSolutionsPage = withSuspense(
  lazy(() => import('../../pages/ProposedSolutionsPage')),
);

export const DashboardShellRoutes = [
  {
    path: ':accountType/launch',
    children: launchChildren,
  },
  {
    path: 'proposed-solutions',
    element: <ProposedSolutionsPage />,
  },
  {
    index: true,
    path: ':accountType/dashboard-overview',
    element: <DashboardOverviewPage />,
  },
  {
    path: ':accountType/dashboard-statements',
    children: statementsChildren,
  },
  {
    path: ':accountType/dashboard-projects',
    children: projectsChildren,
  },
  {
    path: ':accountType/goals',
    element: <GoalsPage />,
  },
  {
    path: ':accountType/dashboard-courses-learn',
    element: <DashboardCoursesPage />,
  },
  {
    path: ':accountType/dashboard-books',
    element: <DashboardBooksPage />,
  },
  {
    path: ':accountType/dashboard-grant',
    children: grantsChildren,
  },
  {
    path: ':accountType/talent',
    children: talentChildren,
  },
  {
    path: ':accountType/dashboard-mentor',
    children: mentorChildren,
  },
  {
    path: ':accountType/dashboard-profile',
    children: profileChildren,
  },
  {
    path: ':accountType/chat',
    element: <ChatPage />,
  },
  {
    path: 'conversations/feed',
    element: <FeedPage />,
  },
  {
    path: 'conversations/channel-chat',
    element: <ChannelChatListPage />,
  },
  {
    path: 'conversations/channel-chat/:channelId',
    element: <ChannelChatPage />,
  },
  {
    path: 'conversations/feed/t/:topicId',
    element: <TopicModalForTopicIdParamsPage />,
  },
  {
    path: 'conversations/channels',
    children: channelsChildren,
  },
  {
    path: ':accountType/teams',
    children: teamsChildren,
  },
  {
    path: ':accountType/feedback-support',
    element: <FeedbackAndSupportPage />,
  },
  {
    path: ':accountType/dashboard-watch',
    element: <DashboardWatchPage />,
  },
  {
    path: ':accountType/dashboard-read',
    element: <DashboardReadPage />,
  },
  {
    path: 'notifications',
    element: <NotificationPage />,
  },
  {
    path: ':accountType/subscriptions',
    children: subscriptionChildren,
  },
  {
    path: ':accountType/applications',
    children: applicationsChildren,
  },
  {
    path: ':accountType/users',
    children: usersChildren,
  },
];
