import {
  ApplicationIcon,
  ChatIcon,
  DashboardIcon,
  Feedbackicon,
  GoalsIcon,
  GrantIcon,
  MentorIcon,
  ProfileIcon,
  ProjectIcon,
  StatementIcon,
  SubscriptionIcon,
  TeamsIcon,
  BooksIcon,
  TalentIcon,
  ConversationIcon,
  UsersIcon,
} from '../../assets/icons';

export const facultySideBarList = [
  { path: 'launch', icon: ApplicationIcon, text: 'Launch It' },
  { path: 'dashboard-overview', icon: DashboardIcon, text: 'Dashboard' },
  { path: 'dashboard-statements', icon: StatementIcon, text: 'Statements' },
  { path: 'goals', icon: GoalsIcon, text: 'Goals Map' },
  { path: 'teams', icon: TeamsIcon, text: 'Teams' },
  { path: 'dashboard-projects', icon: ProjectIcon, text: 'Projects' },
  { path: 'users', icon: UsersIcon, text: 'Users' },
  {
    path: 'conversations/channel-chat',
    icon: ConversationIcon,
    text: 'Conversations',
  },
  { path: 'chat', icon: ChatIcon, text: 'Chat' },
  { path: 'talent', icon: TalentIcon, text: 'Talent' },
  { path: 'dashboard-grant', icon: GrantIcon, text: 'Grants' },
  { path: 'dashboard-courses-learn', icon: StatementIcon, text: 'My Courses' },
  { path: 'dashboard-profile', icon: ProfileIcon, text: 'Profile' },
  { path: 'subscriptions', icon: SubscriptionIcon, text: 'Subscriptions' },
  {
    path: 'feedback-support',
    icon: Feedbackicon,
    text: 'Feedback & Support',
  },
];

export const practitionerSideBarList = [
  { path: 'launch', icon: ApplicationIcon, text: 'Launch It' },
  { path: 'dashboard-overview', icon: DashboardIcon, text: 'Dashboard' },
  { path: 'dashboard-statements', icon: StatementIcon, text: 'Statements' },
  { path: 'goals', icon: GoalsIcon, text: 'Goals Map' },
  { path: 'teams', icon: TeamsIcon, text: 'Teams' },
  { path: 'dashboard-projects', icon: ProjectIcon, text: 'Projects' },
  {
    path: 'conversations/channel-chat',
    icon: ConversationIcon,
    text: 'Conversations',
  },
  { path: 'chat', icon: ChatIcon, text: 'Chat' },
  { path: 'talent', icon: TalentIcon, text: 'Talent' },
  { path: 'dashboard-grant', icon: GrantIcon, text: 'Grants' },
  { path: 'dashboard-courses-learn', icon: StatementIcon, text: 'My Courses' },
  { path: 'dashboard-profile', icon: ProfileIcon, text: 'Profile' },
  { path: 'subscriptions', icon: SubscriptionIcon, text: 'Subscriptions' },
  {
    path: 'feedback-support',
    icon: Feedbackicon,
    text: 'Feedback & Support',
  },
];

export const benefactorSideBarList = [
  { path: 'launch', icon: ApplicationIcon, text: 'Launch It' },
  { path: 'dashboard-overview', icon: DashboardIcon, text: 'Dashboard' },
  { path: 'dashboard-statements', icon: StatementIcon, text: 'Statements' },
  {
    path: 'conversations/channel-chat',
    icon: ConversationIcon,
    text: 'Conversations',
  },
  { path: 'chat', icon: ChatIcon, text: 'Chat' },
  { path: 'dashboard-grant', icon: GrantIcon, text: 'Grants' },
  { path: 'applications', icon: ApplicationIcon, text: 'Applications' },
  { path: 'dashboard-profile', icon: ProfileIcon, text: 'Profile' },
  { path: 'subscriptions', icon: SubscriptionIcon, text: 'Subscriptions' },
  {
    path: 'feedback-support',
    icon: Feedbackicon,
    text: 'Feedback & Support',
  },
];

export const studentSideBarList = [
  { path: 'launch', icon: ApplicationIcon, text: 'Launch It' },
  { path: 'dashboard-overview', icon: DashboardIcon, text: 'Dashboard' },
  { path: 'dashboard-statements', icon: StatementIcon, text: 'Statements' },
  { path: 'goals', icon: GoalsIcon, text: 'Goals Map' },
  { path: 'teams', icon: TeamsIcon, text: 'Teams' },
  { path: 'dashboard-projects', icon: ProjectIcon, text: 'Projects' },
  { path: 'users', icon: UsersIcon, text: 'Users' },
  {
    path: 'conversations/channel-chat',
    icon: ConversationIcon,
    text: 'Conversations',
  },
  { path: 'chat', icon: ChatIcon, text: 'Chat' },
  { path: 'talent', icon: TalentIcon, text: 'Talent' },
  { path: 'dashboard-mentor', icon: MentorIcon, text: 'Mentors' },
  { path: 'dashboard-grant', icon: GrantIcon, text: 'Grants' },
  { path: 'dashboard-courses-learn', icon: StatementIcon, text: 'My Courses' },
  { path: 'dashboard-books', icon: BooksIcon, text: 'uBooks' },
  { path: 'dashboard-profile', icon: ProfileIcon, text: 'Profile' },
  { path: 'subscriptions', icon: SubscriptionIcon, text: 'Subscriptions' },
  {
    path: 'feedback-support',
    icon: Feedbackicon,
    text: 'Feedback & Support',
  },
];
