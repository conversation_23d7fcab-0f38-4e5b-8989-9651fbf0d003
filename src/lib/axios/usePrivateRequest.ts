import { useKeycloak } from '@react-keycloak/web';
import type { AxiosInstance } from 'axios';
import axios from 'axios';
import { useEffect, useRef } from 'react';

export const usePrivateRequest = (baseURL: string) => {
  const axiosInstance = useRef<AxiosInstance>();
  const { keycloak, initialized } = useKeycloak();

  const kcToken =
    keycloak?.token ??
    'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJxUS0xa1FXOW1mZ2thUEh2RFJkdTViaXkwUG82aEVWZGtfazRkOHFPbGJZIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XR-6NO91GZzfptZiKdReeh9gtAsqcTKdHh4FjOcvlrKMN13i4uyANOWKe9QERLMCqN8X6xGQz7eb-qLsFXu2Oju0iHdAeO4-o0nHXSnCDkTxHvlQIm_RmBqry5BtV3I0AyGnYpzLwjGVPz2aoEJso_iP7LXRI9FPhsYZWAUOIE7c8meiYViJHNwYqEDD5kSDF2mrdG9CsHM3XlfLdjUU5mBEoRoi80BHZy2EHDnEXIl9LilUQXGaQ_1Qh8uRaomGJ18TTqRlng0rgCvbBzpq0MfVYpDgY36_YQ8_c23sZRzq6KQUkyNZBbC18iw_avKETJ_seRvUBn9FJUS7W2FHow';

  useEffect(() => {
    axiosInstance.current = axios.create({
      baseURL,
      headers: {
        // eslint-disable-next-line no-constant-condition
        Authorization: true ? `Bearer ${kcToken}` : undefined,
      },
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    axiosInstance.current.defaults.headers.common['Save-Data'] = 'on';

    axiosInstance.current.interceptors.response.use(
      response => response,
      async error => {
        if (error.code === 'ECONNABORTED') {
          console.warn('Request timed out. Retrying...');
          return axiosInstance.current?.request(error.config); // Retry
        }
        return Promise.reject(error);
      },
    );

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, initialized, kcToken]);

  return axiosInstance;
};
