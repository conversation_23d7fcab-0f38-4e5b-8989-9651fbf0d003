import { ComponentProps } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import { OverrideProp as OverrideProperty } from '../../types';

type InputProps = OverrideProperty<
  ComponentProps<'input'>,
  {
    name: string;
  }
>;
interface Props extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>;
}

export default function RadioButton({
  registerHanlder,
  ...rest
}: Omit<Props, 'name' | 'type'>) {
  return (
    <>
      <input
        className="border-primary checked:border checked:!bg-primary"
        type="radio"
        {...rest}
        {...registerHanlder()}
      />
    </>
  );
}
