import { ComponentProps, forwardRef, MutableRefObject, useRef } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import { OverrideProp as OverrideProperty } from '../../types';

type InputProps = OverrideProperty<
  ComponentProps<'input'>,
  {
    name: string;
  }
>;
interface Props extends InputProps {
  registerHanlder: () => UseFormRegisterReturn<string>;
}

/* eslint-disable react/display-name */
export const FormInput = forwardRef<HTMLInputElement, Omit<Props, 'name'>>(
  ({ registerHanlder, ...rest }, ref) => {
    const localRef = useRef<HTMLInputElement>(null);
    const insideRef = ref || localRef;
    const { ref: formHandlerRef, ...formRest } = registerHanlder();
    return (
      <>
        <input
          {...rest}
          {...formRest}
          ref={e => {
            formHandlerRef(e);
            (insideRef as MutableRefObject<HTMLInputElement>).current = e!;
          }}
        />
      </>
    );
  },
);
