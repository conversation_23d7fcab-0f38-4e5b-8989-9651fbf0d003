import { ComponentProps, ReactNode } from 'react';
import { Controller, FieldErrors } from 'react-hook-form';

import { UploadIcon } from '../../assets/icons';
import { cn } from '../../lib/twMerge/cn';
import { FormDataType } from '../../types';
import FormFileInput from './FormFileInput';

type InputProps = ComponentProps<typeof FormFileInput>;

interface Props<T extends FormDataType> extends InputProps {
  errors: FieldErrors<T>;
  name: keyof T;
  labelName?: string;
  formLabel?: ReactNode;
  control: any;
}

export default function FormFileInputBox<T extends FormDataType>({
  name,
  errors,
  control,
  labelName,
  formLabel: AltFormLabel,
  ...rest
}: Props<T>) {
  return (
    <>
      {AltFormLabel ? (
        AltFormLabel
      ) : (
        <p
          className={`after:text-[16px] after:text-primary after:content-["*"]`}
        >
          {labelName}
        </p>
      )}
      <Controller
        control={control}
        name={name as string}
        render={({ field: { onChange, onBlur, value } }) => (
          <div className="mt-2 flex">
            <label
              htmlFor={name as string}
              className={cn(`input flex cursor-pointer items-center gap-x-4`)}
            >
              <FormFileInput
                id={name as string}
                type="file"
                onChange={e => onChange(e?.target?.files?.[0])}
                className="hidden"
                onBlur={onBlur}
                {...rest}
              />
              <UploadIcon className="h-6 w-6" />
              {value ? (
                <p className="truncate">{value?.name?.slice(0, 15)}</p>
              ) : (
                <p className="truncate text-gray-35">{labelName}</p>
              )}
            </label>
          </div>
        )}
      />
      {errors[name as keyof FieldErrors<T>] && (
        <p className="mt-2 text-[11px] text-red-800 sm:text-[14px]">
          {errors[name as keyof FieldErrors<T>]?.message as ReactNode}
        </p>
      )}
    </>
  );
}
