import { useKeycloak } from '@react-keycloak/web';
import { useEffect } from 'react';
import { useUserContext } from '../../../context/user/UserContext';
import { Helper } from '../../../utils/helpers';
import { AppOutlet } from './AppOutlet';

export default function UserOnboardingNavigationShell() {
  const { keycloak } = useKeycloak();
  const { isLoggedIn } = useUserContext();
  useEffect(() => {
    // eslint-disable-next-line no-constant-condition
    if (!true) {
      Helper.setLocalUserOnboardingLevel(1);
      keycloak.login({ redirectUri: window.location.href });
    }
  }, [isLoggedIn]);
  return <AppOutlet />;
}
