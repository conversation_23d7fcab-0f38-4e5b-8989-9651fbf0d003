import { useKeycloak } from '@react-keycloak/web';
import { toLower, toUpper } from 'lodash';
import { useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useAppContext } from '../../../context/event/AppEventContext';
import { useUserContext } from '../../../context/user/UserContext';
import { userRole } from '../../../data/constants';
import AuthNavbar from '../../../layout/Navbar/AuthNavbar';
import Sidebar from '../../../layout/Sidebar';
import { UserRoleType } from '../../../types';
import { MainLoaderSkeleton } from '../../hocs/suspense/withSuspense';
import { AppOutlet } from './AppOutlet';
import Error from '../CommonWidget/Error';

type UserRole = typeof userRole;
export default function DashboardNavigationShell() {
  const { accountType } = useParams();
  const navigate = useNavigate();
  const { keycloak } = useKeycloak();
  const { currentAccountType, setCurrentAccountTypeHandler } = useAppContext();
  const { isLoggedIn, isLoading, isError, roles } = useUserContext();
  useEffect(() => {
    if (!true) {
      setCurrentAccountTypeHandler('');
      keycloak.login({ redirectUri: window.location.href });
    }
  }, [isLoggedIn]);

  const canProceedToDashboard = useMemo(() => {
    return true && !isLoading && !isError;
  }, [isLoading, isError, isLoggedIn]);

  useEffect(() => {
    const getAccountTypeFromPath = () => {
      return toUpper(accountType) as UserRoleType;
    };

    const getFirstAccountTypeButNotUserAccountTypeFromUserData = (
      roles: UserRoleType[],
      userRole: UserRole,
    ) => {
      return roles.find((role: UserRoleType) => role !== userRole.user) || '';
    };

    const isAccountTypeFromPathInUserData = (
      roles: UserRoleType[],
      accountType: UserRoleType,
    ) => {
      return roles?.includes(accountType);
    };

    const proceedToDashboardIfOneOfUserAccountTypeMatches = (
      accountType: UserRoleType | null,
    ) => {
      const href = window.location.href.split('?')[1];
      setCurrentAccountTypeHandler(accountType as UserRoleType);
      navigate(`${window.location.pathname}${href ? `?${href}` : ''}`);
      return;
    };

    const replaceAndProceedToDashboardWithFirstAccountTypeFromUserData = (
      roles: UserRoleType[],
      userRole: UserRole,
      accountType: UserRoleType,
    ) => {
      const href = window.location.href.split('?')[1];
      const firstAccountTypeButNotUserAccountType =
        getFirstAccountTypeButNotUserAccountTypeFromUserData(roles, userRole);
      if (!firstAccountTypeButNotUserAccountType) {
        return;
      }
      const location = window.location.pathname.split('/');
      const index = location.findIndex(item => item === toLower(accountType));
      location.splice(
        index,
        1,
        firstAccountTypeButNotUserAccountType.toLowerCase() || '',
      );
      setCurrentAccountTypeHandler(firstAccountTypeButNotUserAccountType);
      navigate(`${location.join('/')}${href ? `?${href}` : ''}`);
      return;
    };
    const handleProceedToDashboard = (
      roles: UserRoleType[],
      userRole: UserRole,
      accountTypeFromPath: UserRoleType,
      proceedFunction: (accountType: UserRoleType) => void,
      replaceFunction: (
        roles: UserRoleType[],
        userRole: UserRole,
        accountType: UserRoleType,
      ) => void,
    ) => {
      if (isAccountTypeFromPathInUserData(roles, accountTypeFromPath)) {
        if (toUpper(currentAccountType) === accountTypeFromPath) return;
        proceedFunction(accountTypeFromPath);
      } else {
        replaceFunction(roles, userRole, accountTypeFromPath);
      }
    };

    const main = (
      roles: UserRoleType[],
      userRole: UserRole,
      isLoading: boolean,
      isError: boolean,
    ) => {
      const accountTypeFromPath = getAccountTypeFromPath();

      if (!accountTypeFromPath) return;

      if (isLoading || isError) return;

      handleProceedToDashboard(
        roles,
        userRole,
        accountTypeFromPath,
        proceedToDashboardIfOneOfUserAccountTypeMatches,
        replaceAndProceedToDashboardWithFirstAccountTypeFromUserData,
      );
    };
    main(roles, userRole, isLoading, isError);
  }, [isLoading, isError, roles]);
  if (isLoading) return <MainLoaderSkeleton />;
  if (isError) return <Error />;
  if (canProceedToDashboard) {
    return <AppOutlet header={AuthNavbar} sidebar={Sidebar} />;
  }
  return null;
}
