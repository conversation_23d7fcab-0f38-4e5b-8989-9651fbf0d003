import { PencilIcon } from '@/features/Profile/assets';

import useGetProjectLevelBasedAccess from '@/hooks/useGetProjectLevelBasedAccess';
import { useAppContext } from '@/context/event/AppEventContext';

export default function ProjectDescription({
  hideEdit = false,
  description,
}: {
  hideEdit?: boolean;
  description: string | undefined;
}) {
  const { setShowModalHandler } = useAppContext();
  const { isLoggedInUserAProjectAdmin, isCreatorAtProjectLevel } =
    useGetProjectLevelBasedAccess();

  return (
    <div className="text-[16px] leading-[26px] text-stone-950">
      <div className="mb-4 flex items-center justify-between">
        <h5 className="text-primary">Project Description</h5>
        {!hideEdit && (
          <>
            {(isLoggedInUserAProjectAdmin || isCreatorAtProjectLevel) && (
              <button
                className="flex cursor-pointer items-center gap-2 leading-none text-primary"
                onClick={() =>
                  setShowModalHandler('UpdateProjectInformationModal')
                }
              >
                Edit Project <PencilIcon className="stroke-primary" />
              </button>
            )}
          </>
        )}
      </div>
      {description ? (
        <p className="text-[14px] font-[400]">Introduction: {description}</p>
      ) : (
        <p className="text-[12px]">N/A</p>
      )}
    </div>
  );
}
