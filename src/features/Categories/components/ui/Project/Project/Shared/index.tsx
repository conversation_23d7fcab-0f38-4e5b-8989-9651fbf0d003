import { useMemo } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';

import {
  DuplicateTaskModal,
  GoalsListPresenceContainer,
  MoveTaskToObjectiveModal,
} from '@/features/projectManagementGoals';

import {
  noImagePlaceholder,
  notFound,
} from '../../../../../../../assets/images';
import ProblemStatementDescriptions from '../../../../../../../components/ui/CategoryWidget/ProblemStatement/ProblemStatementDescriptions';
import CreatorWidget from '../../../../../../../components/ui/CommonWidget/CreatorWidget';
import { Spinner } from '../../../../../../../components/ui/CommonWidget/Loader';
import Player from '../../../../../../../components/ui/WatchWidget/player';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import { useGetProblemStatement } from '../../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import useGetProjectLevelBasedAccess from '../../../../../../../hooks/useGetProjectLevelBasedAccess';
import { useHandleQueryParams } from '../../../../../../../hooks/useHandleQueryParams';
import {
  ViewAndEditTaskCard,
  ViewAndEditTaskPresenceContainer,
} from '../../../../../../projectManagementGoals';
import { useGetProject } from '../../../../../hooks/apiQueryHooks/eduQueryHooks';
import ProjectImageAndDescription from './ProjectImageAndDescription';
import TeamsPreview from './TeamsPreview';
import { DeleteProjectModal } from '@/features/projectManagementGoals';
import ProjectExtraInfomation from '@/features/Categories/components/ui/Project/Project/Shared/ProjectExtraInfomation';
import { ProjectObjectiveList } from '@/features/projectManagementGoals';
import { AddOrEditObjectivesModal } from '@/features/projectManagementGoals';
import { DeleteObjectiveModal } from '@/features/projectManagementGoals';
import Button from '@/components/ui/ButtonComponent';
import { AddProblemStatementToProjectModal } from '@/features/projectManagementGoals';
import {
  ProjectAttachment,
  ProjectStatusValues,
  VisibilityType,
} from '@/types';
import UpdateProjectVisibilityModal from '../../../Modals/UpdateProjectVisibilityModal';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { DocIcon, PdfIcon } from '@/features/Chat/assets/icons';
import CommonDashedBorderBoxWithActions from '@/components/ui/CommonWidget/CommonDashedBorderBoxWithActions';
import { PencilIcon } from '@/features/Profile/assets';
import { DeleteIcon, DeleteIcon2 } from '@/assets/icons';
import { DownloadIconTwo } from '@/features/WatchVideo/assets/icons';
import { ToolTipWrapper } from '@/components/ui/TooltipWidget/TooltipWidget';
import { DeleteProjectFileModal } from '@/features/projectManagementGoals';
import { DeleteTaskModal } from '@/features/projectManagementGoals';

export function SharedProject({
  header: Header,
}: {
  header: React.FC<{
    projectName: string;
    visibility: VisibilityType;
    projectStatus?: ProjectStatusValues;
  }>;
}) {
  const { projectRef } = useParams();
  const { query, handleQuery } = useHandleQueryParams();
  const parameterProjectRef = query.get('project_ref');
  const ref = projectRef || parameterProjectRef;
  const navigate = useNavigate();
  const {
    isCreatorAtProjectLevel,
    isLoggedInUserAProjectAdmin,
    isLoggedInUserAProjectMember,
  } = useGetProjectLevelBasedAccess();
  const { setShowModalHandler, showModal } = useAppContext();
  const { data: project, isLoading } = useGetProject(
    projectRef || parameterProjectRef || '',
  );

  const { data: problemStatement } = useGetProblemStatement(
    project?.data?.problemStatementRef || '',
    {
      enabled: !!project?.data?.problemStatementRef,
    },
  );

  const videoUrls = useMemo(() => {
    return project?.data?.descriptionVideoUrls || [];
  }, [project]);

  const documents = useMemo(() => {
    return project?.data?.documents || [];
  }, [project]);

  const handleFileDelete = (attachment: ProjectAttachment, type: string) => {
    handleQuery({
      type,
      project_ref: projectRef,
      file_name: attachment?.name,
      attachement_ref: attachment?.fileRef,
    });
    setShowModalHandler('DeleteProjectFileModal');
  };

  if (isLoading)
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Spinner className="" />
      </div>
    );
  return (
    <>
      <section className="flex flex-col items-stretch pb-16">
        <Header
          projectStatus={project?.data?.projectStatus}
          projectName={project?.data?.projectName || 'N/A'}
          visibility={project?.data.visibility || 'PRIVATE'}
        />
        <div className="mb-2">
          <CreatorWidget createdBy={project?.data?.createdByName || ''} />
        </div>
        {project ? (
          <div className="mb-[50px] border border-grayThirteen bg-white p-6 ">
            <ProjectImageAndDescription project={project?.data} />
            <ProjectExtraInfomation project={project?.data} />
          </div>
        ) : (
          <div className="flex h-[100px] w-full items-center justify-center">
            <Spinner />
          </div>
        )}
        <div className="flex items-stretch justify-between  gap-6 max-sm:flex-col">
          <div className="w-full max-w-[525px] bg-white p-6 shadow-md">
            <h3 className="mb-4 font-semibold">Project Teams</h3>
            <div className="overflow-auto">
              <div id="project-teams" className="max-h-[400px]">
                <TeamsPreview />
              </div>
            </div>
          </div>
          <div className="w-full max-w-[525px]">
            {problemStatement ? (
              <>
                <div className="mb-4 text-[16px] leading-[27px]">
                  <div className="mb-4 flex w-full items-center justify-between">
                    <span className="font-semibold">Problem statement</span>
                    {isLoggedInUserAProjectAdmin && (
                      <Button
                        onClick={() =>
                          navigate('add-problem-statement-to-project')
                        }
                        type="button"
                        className="group flex max-h-8 w-full max-w-24 items-center gap-x-2 whitespace-nowrap border-[1px] border-black bg-transparent px-2.5 py-1.5 hover:border-primary hover:bg-primary hover:text-white"
                      >
                        <p className="text-sm text-grayNine group-hover:text-white">
                          Update
                        </p>
                        <PencilIcon className="size-4 shrink-0 stroke-grayNine group-hover:stroke-white" />
                      </Button>
                    )}
                  </div>
                  <span className="text-primary">
                    {problemStatement?.data.title}
                    {problemStatement?.data.title}
                  </span>
                </div>
                <div className="relative aspect-[2] max-w-[502px] rounded-[10px]">
                  <Player url={problemStatement?.data?.descVidUrl || ''} />
                </div>
                <ProblemStatementDescriptions
                  description={problemStatement?.data?.description}
                  fileTitle={problemStatement?.data.title || ''}
                  fileUrl={problemStatement?.data.descDocUrl || ''}
                />
              </>
            ) : (
              <div
                className={` whitespace-nowrap rounded-sm border
              border-dashed border-grayNine bg-[#FCFBFF] bg-opacity-10 p-6  max-md:px-5`}
              >
                <h3 className="mb-4 font-semibold">Problem statement</h3>
                <div className="flex items-center justify-center px-16  text-center text-sm text-neutral-600">
                  <div>
                    <img
                      src={notFound}
                      alt=" not found"
                      className="mx-auto mb-[2rem]"
                    />
                    <div className="mb-4 text-center text-sm font-medium text-neutral-600">
                      {'No problem statement added to this project'}
                    </div>
                    {isLoggedInUserAProjectAdmin && (
                      <Button
                        onClick={() =>
                          navigate('add-problem-statement-to-project')
                        }
                        className="group w-full min-w-[154px] whitespace-nowrap border-[1px] border-grayTen bg-transparent hover:border-primary  hover:bg-primary hover:text-white"
                      >
                        Add a problem statement
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {videoUrls?.length > 0 || documents?.length > 0 ? (
          <div className="mt-6 max-w-[525px]">
            <h5 className=" mb-4 text-[16px] font-semibold leading-[27px]">
              Attached Files ({documents?.length || 0})
            </h5>
            <div className="flex flex-col justify-between gap-5 sm:flex-row">
              {videoUrls?.length > 0 && (
                <div className="mb-10 flex w-full max-w-[245px] flex-col justify-center bg-white p-4 shadow-[0px_3.3px_8.24px_0px_#0000001A]">
                  <div className="group/first relative mb-2.5 h-[130px] rounded-[4px] bg-white max-md:w-full">
                    <Player
                      url={videoUrls?.[videoUrls?.length - 1]?.url || ''}
                    />
                    <div className="absolute right-1 top-1 z-[1] flex items-center gap-1 opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                      <ToolTipWrapper
                        className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                        text="Delete File"
                      >
                        <span
                          onClick={() =>
                            handleFileDelete(
                              videoUrls?.[videoUrls?.length - 1],
                              'video',
                            )
                          }
                        >
                          <DeleteIcon2 className="h-4 w-4" />
                        </span>
                      </ToolTipWrapper>
                    </div>
                  </div>{' '}
                  <p className="mb-0 text-center text-[14px] font-medium">
                    {project?.data?.projectName} video pitch
                  </p>
                </div>
              )}
              {documents?.length > 0 && (
                <div>
                  <div className="mb-4 flex max-h-[200px] flex-col gap-4 overflow-auto">
                    {documents?.map(attachment => (
                      <div
                        key={attachment.fileRef}
                        className="flex justify-between gap-3"
                      >
                        <div className="flex gap-3">
                          {GoalsHelper.isImage(attachment?.url) && (
                            <img
                              loading="lazy"
                              src={attachment?.url || noImagePlaceholder}
                              className="h-7 w-6  self-start overflow-hidden object-cover object-center"
                            />
                          )}
                          {GoalsHelper.isPdf(attachment?.url) && (
                            <div className="flex h-7 w-6 items-center justify-center">
                              <PdfIcon className="size-full" />
                            </div>
                          )}
                          {GoalsHelper.isDoc(attachment?.url) && (
                            <div className=" flex h-7 w-6 items-center justify-center">
                              <DocIcon className="size-full" />
                            </div>
                          )}
                          <div className="flex flex-col gap-1">
                            <p className="text-sm font-medium">
                              {attachment?.name}
                            </p>
                            <span className="text-sm text-grayFourteen">
                              {attachment?.size}
                            </span>
                          </div>
                        </div>
                        {(isCreatorAtProjectLevel ||
                          isLoggedInUserAProjectAdmin) && (
                          <div className="flex gap-3">
                            <ToolTipWrapper text="Download">
                              <DownloadIconTwo
                                onClick={() =>
                                  window.open(attachment?.url, '_blank')
                                }
                                className="size-4 shrink-0 cursor-pointer stroke-grayNine hover:stroke-primary"
                              />
                            </ToolTipWrapper>
                            <ToolTipWrapper text="Delete File">
                              <DeleteIcon
                                onClick={() =>
                                  handleFileDelete(attachment, 'file')
                                }
                                className="size-4 shrink-0 cursor-pointer stroke-grayNine hover:stroke-primary"
                              />
                            </ToolTipWrapper>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <CommonDashedBorderBoxWithActions
            className="mt-6 max-w-[525px] border-grayThirteen bg-white"
            buttonText="Add Files"
            message="No files yet"
            {...(isLoggedInUserAProjectAdmin && {
              buttonAction: () =>
                setShowModalHandler('UpdateProjectAttachmentModal'),
            })}
          />
        )}
        {isLoggedInUserAProjectMember && (
          <div className="mt-6">
            <h5 className=" mb-4 text-[16px] font-semibold leading-[27px]">
              Objectives
            </h5>
            <div className="flex gap-2">
              <GoalsListPresenceContainer>
                <div className="bg-lightYellowTwo p-2">
                  <ProjectObjectiveList projectRef={ref || ''} />
                </div>
                <motion.span
                  initial={{ opacity: 0, x: '-30px' }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5, type: 'tween' }}
                  className="my-4 flex cursor-pointer items-center px-3 text-[12px] max-xs:justify-between max-xs:text-[10px] xs:gap-x-3 xs:px-8 sm:gap-x-6"
                >
                  <p
                    onClick={() => {
                      handleQuery({
                        project_ref: projectRef,
                      });
                      setShowModalHandler('AddOrEditObjectiveModal');
                    }}
                    className="duration-200 ease-out hover:text-primary"
                  >
                    Add Objective
                  </p>
                </motion.span>
              </GoalsListPresenceContainer>
              <ViewAndEditTaskPresenceContainer>
                <ViewAndEditTaskCard />
              </ViewAndEditTaskPresenceContainer>
            </div>
          </div>
        )}
        <DeleteProjectFileModal />
        <DeleteObjectiveModal />
        <AddOrEditObjectivesModal />
        <DeleteTaskModal />
        <AddProblemStatementToProjectModal />
        <AnimatePresence>
          {showModal === 'DeleteProjectModal' && <DeleteProjectModal />}
        </AnimatePresence>
        <UpdateProjectVisibilityModal />
        <DuplicateTaskModal />
        <MoveTaskToObjectiveModal />
      </section>
    </>
  );
}
