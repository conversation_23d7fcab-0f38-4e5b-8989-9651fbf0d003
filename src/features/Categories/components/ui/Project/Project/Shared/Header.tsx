import { useNavigate, useParams } from 'react-router-dom';

import Button from '../../../../../../../components/ui/ButtonComponent';
import CommonHeaderWithBackArrow from '../../../../../../../components/ui/CommonWidget/CommonHeaderWithBackArrow';
import { useAppContext } from '../../../../../../../context/event/AppEventContext';
import useGetProjectLevelBasedAccess from '../../../../../../../hooks/useGetProjectLevelBasedAccess';
import useHandleApiFeebackWithToast from '../../../../../../../hooks/useHandleApiFeebackWithToast';
import { useHandleQueryParams } from '../../../../../../../hooks/useHandleQueryParams';
import {
  GET_GOAL_DETAILS_QUERY,
  GET_PROJECT_QUERY,
} from '../../../../../../../utils/queryKeys';
import { useAddProjectToGoal } from '../../../../../../projectManagementGoals';
import { useRemoveProjectFromGoal } from '../../../../../../projectManagementGoals/hooks/apiQueryhooks/eduQueryHooks';
import useGetGoalDetailsData from '../../../../../../projectManagementGoals/hooks/useGetGoalDetailsData';
import { ProjectStatusValues, VisibilityType } from '@/types';
import {
  ArrowDownIcon,
  ClosedPadlockIcon,
  OpenedPadloackIcon,
} from '@/assets/icons';
import { TooltipWidget } from '@/components/ui/TooltipWidget/TooltipWidget';
import { useRef, useState } from 'react';
import { cn } from '@/lib/twMerge/cn';
import { visibility as visibilityValue } from '@/data/constants';
import { AnimatePresence } from 'framer-motion';
import SlideUpModalCase from '@/components/ui/Modals/SlideUpModalCase';
import ProjectCTADropDown from './ProjectCTADropDown';
import { Helper } from '@/utils/helpers';
import { toUpper } from 'lodash';

export default function Header({
  projectName,
  projectStatus,
  visibility,
}: {
  projectName: string;
  visibility: VisibilityType;
  projectStatus?: ProjectStatusValues;
}) {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { handleQuery } = useHandleQueryParams();
  const { showModal, currentAccountType, setShowModalHandler } =
    useAppContext();
  const {
    isLoggedInUserAProjectMember,
    isLoggedInUserAProjectAdmin,
    isCreatorAtProjectLevel,
  } = useGetProjectLevelBasedAccess();
  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      <CommonHeaderWithBackArrow
        route={`/${currentAccountType}/dashboard-projects`}
      >
        <h4 className="flex max-w-[788px] items-center gap-2 text-[20px] font-[500] text-black max-xs:text-[14px]">
          {projectName}{' '}
          {visibility === 'PRIVATE' ? (
            <span
              ref={cardRef}
              onMouseOver={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={() => {
                setShowModalHandler('UpdateProjectVisibilityModal');
                handleQuery({
                  visibility: visibilityValue.PUBLIC,
                  project_name: projectName,
                });
              }}
              className="flex h-5 cursor-pointer items-center justify-center gap-1 rounded-[4px] bg-black px-[4px]"
            >
              <ClosedPadlockIcon className="h-3 w-3" />
              <p className="mt-[2px] cursor-pointer text-[10px] font-semibold leading-[10px] text-white">
                Private
              </p>
              <TooltipWidget
                parentRef={cardRef.current}
                show={isHovered}
                className={cn(
                  `z-[5] max-w-[230px] rounded-[8px] bg-primary p-3 text-center text-[11px] text-white max-xs:hidden`,
                )}
              >
                Private projects can be seen by all team members. Click to
                update.
              </TooltipWidget>
            </span>
          ) : (
            <span
              ref={cardRef}
              onMouseOver={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={() => {
                setShowModalHandler('UpdateProjectVisibilityModal');
                handleQuery({
                  visibility: visibilityValue.PRIVATE,
                  project_name: projectName,
                });
              }}
              className="flex h-5 cursor-pointer items-center justify-center gap-1 rounded-[4px] bg-green px-[4px]"
            >
              <OpenedPadloackIcon className="h-3 w-3" />
              <p className="mt-[2px] text-[10px] font-semibold leading-[10px] text-white">
                Public
              </p>
              <TooltipWidget
                parentRef={cardRef.current}
                show={isHovered}
                className={cn(
                  `z-[5] max-w-[375px] rounded-[8px] bg-primary p-3 text-center text-[11px] text-white max-xs:hidden`,
                )}
              >
                Public projects can be seen by all users seeking global
                innovation collaboration partners. Click to update.
              </TooltipWidget>
            </span>
          )}
        </h4>
      </CommonHeaderWithBackArrow>
      <div className="flex w-full max-w-[400px] flex-wrap items-center gap-2 md:gap-4">
        <div className="flex items-center gap-2 text-sm font-medium">
          <p className="whitespace-nowrap">Project Status: </p>
          <div
            className={cn(
              'min-w-14 rounded-sm p-1 text-center text-[10px] font-semibold leading-3',
              Helper.getProjectStatusAppearance(
                Helper.capitalizeString(projectStatus),
              ),
            )}
          >
            {projectStatus
              ? toUpper(Helper.capitalizeString(projectStatus))
              : 'N/A'}
          </div>
        </div>
        {(isLoggedInUserAProjectAdmin || isLoggedInUserAProjectMember) && (
          <Button
            onClick={() => setShowModalHandler('ProjectCTADropDown')}
            className="group w-full max-w-[110px] whitespace-nowrap border-[1px] border-grayTen bg-transparent hover:border-primary  hover:bg-primary hover:text-white"
          >
            <p className="text-[16px] leading-8 text-grayTen group-hover:text-white">
              Actions
            </p>
            <ArrowDownIcon className="ml-2 shrink-0 stroke-grayTen group-hover:stroke-white" />
          </Button>
        )}
        {!isCreatorAtProjectLevel && (
          <>
            {!isLoggedInUserAProjectMember && (
              <Button
                onClick={() => {
                  navigate(
                    `/${currentAccountType}/dashboard-projects/${projectRef}/request-team-to-join-project?projectName=${projectName}`,
                  );
                }}
                className="w-full max-w-[204px] bg-primary text-[12px] 
              text-white hover:bg-primary"
              >
                <p>Request to join</p>
              </Button>
            )}
          </>
        )}
      </div>
      <AnimatePresence>
        {showModal === 'ProjectCTADropDown' && (
          <>
            <SlideUpModalCase className="absolute right-8 top-[110px] z-[10000] sm:top-[90px]">
              <ProjectCTADropDown />
            </SlideUpModalCase>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}

export const SeeMoreAboutProjectForGoalsHeader = ({
  projectName,
}: {
  projectName: string;
}) => {
  const { query } = useHandleQueryParams();
  const { setShowModalHandler } = useAppContext();
  const { mutate, isLoading } = useAddProjectToGoal({
    ...useHandleApiFeebackWithToast({
      queryKey: [GET_GOAL_DETAILS_QUERY, GET_PROJECT_QUERY],
    }),
  });
  const { mutate: removeProject } = useRemoveProjectFromGoal({
    ...useHandleApiFeebackWithToast({
      queryKey: [GET_GOAL_DETAILS_QUERY, GET_PROJECT_QUERY],
    }),
  });
  const handleSelect = () => {
    mutate({
      projectRef: query.get('project_ref') || '',
      goalRef: query.get('goal_ref') || '',
    });
  };
  const handleUnSelect = () => {
    removeProject({
      projectRef: query.get('project_ref') || '',
      goalRef: query.get('goal_ref') || '',
    });
  };

  const handleApiCall = () => {
    if (isSelected) {
      handleUnSelect();
    } else {
      handleSelect();
    }
  };

  const { data } = useGetGoalDetailsData({
    goalRef: query.get('goal_ref') || '',
    createdBy: query.get('created_by') || '',
  });
  const isSelected = data?.data.projects
    .map(project => project?.projectRef)
    .includes(query.get('project_ref') || '');

  const handleCloseModal = () => {
    query.get('viewProject')
      ? setShowModalHandler('')
      : setShowModalHandler('SelectProjectModal');
  };

  const { isCreatorAtProjectLevel } = useGetProjectLevelBasedAccess();
  return (
    <div className="flex items-center justify-between">
      <div onClick={() => handleCloseModal()}>
        <CommonHeaderWithBackArrow>
          <h4 className="max-w-[788px] text-[20px] font-[500] text-black">
            {projectName}
          </h4>
        </CommonHeaderWithBackArrow>
      </div>
      {isCreatorAtProjectLevel && (
        <div className="flex items-center gap-x-1 md:gap-x-4">
          <Button
            disabled={isLoading}
            onClick={() => {
              handleApiCall();
            }}
            type="button"
            className="group w-full min-w-[154px] whitespace-nowrap border-[1px] border-grayTen bg-transparent 
            hover:border-primary hover:bg-primary hover:text-white disabled:cursor-not-allowed  disabled:bg-transparent disabled:text-primary"
          >
            <p className="text-[16px] leading-8">
              {isSelected ? 'Remove This Project' : 'Add This Project'}
            </p>
          </Button>
        </div>
      )}
    </div>
  );
};
