import React, { ReactElement } from 'react';

import { Project } from '../../../../../types';
import { cn } from '@/lib/twMerge/cn';

type Props = {
  projectList: Project[];
  isAvailableProjectVariant?: boolean;
  children: ReactElement<{ project: Project; index: number }>;
  className?: string;
};

export default function ProjectCategoryList({
  className,
  children,
  projectList,
}: Props) {
  const cloneElementWithProject = (
    child: ReactElement<{ project: Project; index: number }>,
    project: Project,
    index: number,
  ) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        project,
        index,
        key: project?.projectRef,
      });
    }
    return child;
  };

  return (
    <div
      className={cn(`flex gap-3 px-2 pb-4 max-md:items-stretch ${className}`)}
    >
      {projectList?.map((project, index) => {
        return React.Children.map(children, child =>
          cloneElementWithProject(
            child,
            {
              ...project,
            },
            (index = index + 1),
          ),
        );
      })}
    </div>
  );
}
