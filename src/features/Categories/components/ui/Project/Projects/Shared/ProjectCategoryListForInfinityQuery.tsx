import React, { ComponentProps, forwardRef, ReactElement, Ref } from 'react';

import { InfiniteData } from '@tanstack/react-query';
import { ServerProjects } from '@/types';
import { Project } from '@/features/Categories/types';
import { cn } from '@/lib/twMerge/cn';

interface Props extends ComponentProps<'div'> {
  pages: InfiniteData<ServerProjects>['pages'];
  children: ReactElement<{
    project: Project;
    pageIndex: number;
    index: number;
    ref: Ref<HTMLDivElement>;
  }>;
  isAvailableProjectVariant?: boolean;
  className?: string;
}

export default forwardRef(function ProjectCategoryListForInfinityQuery(
  { children, pages, className }: Props,
  ref: Ref<HTMLDivElement>,
) {
  const cloneElementWithTeam = (
    child: ReactElement<{
      project: Project;
      pageIndex: number;
      index: number;
      ref: Ref<HTMLDivElement>;
    }>,
    project: Project,
    pageIndex: number,
    index: number,
    ref?: Ref<HTMLDivElement>,
  ) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        project,
        pageIndex,
        key: project?.projectRef,
        index,
        ref,
      });
    }
    return child;
  };
  return (
    <div className="flex gap-3 max-md:items-stretch">
      {pages?.map((page, pageIndex) => (
        <div
          key={pageIndex}
          className={cn(
            `flex gap-3 px-2 pb-4 max-md:items-stretch ${className}`,
          )}
        >
          {page?.data?.projects?.map((project, index) => {
            if (page.data.projects?.length == index + 1) {
              return React.Children.map(children, child =>
                cloneElementWithTeam(
                  child,
                  project,
                  pageIndex,
                  (index = index + 1),
                  ref,
                ),
              );
            }
            return React.Children.map(children, child =>
              cloneElementWithTeam(
                child,
                project,
                pageIndex,
                (index = index + 1),
              ),
            );
          })}
        </div>
      ))}
    </div>
  );
});
