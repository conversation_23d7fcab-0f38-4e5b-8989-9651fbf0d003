import { uniqueId } from 'lodash';
import { useCallback, useRef, useState } from 'react';
import 'react-phone-input-2/lib/bootstrap.css';
import { useNavigate } from 'react-router-dom';
import { Controller, SubmitHandler } from 'react-hook-form';

import {
  ArrowBackIcon,
  ArrowDownIcon,
  AttachmentIcon,
  DeleteIcon2,
} from '../../../../../../assets/icons';

import Form from '../../../../../../components/forms/Form';
import FormSelectBox from '@/components/forms/FormSelectBox';
import FormLabel from '../../../../../../components/forms/FormLabel';
import FormDatePickerBox from '@/components/forms/FormDatePickerBox';
import FormInputBox from '../../../../../../components/forms/FormInputBox';
import SharedFormButton from '../../../../../../components/ui/SharedFormButton';
import FormTextAreaBox from '../../../../../../components/forms/FormTextAreaBox';
import FormFileInputBox from '../../../../../../components/forms/FormFileInputBox';

import {
  useGetSDGs,
  useGetProblemStatement,
  useGetProblemStatements,
} from '../../../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useAddProject } from '../../../../hooks/apiQueryHooks/eduQueryHooks';
import { useAppContext } from '../../../../../../context/event/AppEventContext';
import { useHandleQueryParams } from '../../../../../../hooks/useHandleQueryParams';
import { useCreateStatementCategoriesOptions } from '@/hooks/useCreateStatementCategoriesOptions';
import useCreateProblemStatementsArr from '@/features/Categories/hooks/useCreateProblemStatementsArr';
import { useFormDataAndApiMutateHandler } from '../../../../../../hooks/useFormDataAndApiMutateHandler';

import { Helper } from '@/utils/helpers';
import { DataResponse } from '../../../../../../types';
import { IAddProjectPayload } from '../../../../types';
import { GET_PROJECTS_QUERY } from '@/utils/queryKeys';
import { projectCosts, projectLevels } from '@/features/Categories/data/data';
import { iAddProjectSchema as IAddProjectSchema } from '../../../../lib/yup/validations';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { ProjectProgressOptions } from '@/features/projectManagementGoals';
import { projectProgressStatus } from '@/data/constants';
import FormInputBoxWithForwardRef from '../../../../../../components/forms/FormInputBox';
import Button from '@/components/ui/ButtonComponent';
import { cn } from '@/lib/twMerge/cn';
import AddProjectDocumentModal from '../../../ui/Modals/AddProjectDocumentModal';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { DocIcon, PdfIcon } from '@/features/Chat/assets/icons';
import { noImagePlaceholder } from '@/assets/images';
import ToolTip from '@/components/ui/CommonWidget/ToolTip';
import { useCreateVisibilityOptions } from '@/hooks/useCreateVisibilityOptions';

const defaultValues = {
  projectName: '',
  projectCountries: [''],
  sdgCategoryRefs: [''],
  projectLevel: '',
  projectDescription: '',
  projectImage: undefined,
  problemStatementRef: '',
  startDate: null,
  endDate: null,
  projectStatus: projectProgressStatus.To_Do,
};
export function SharedAddProjectForm() {
  const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);
  const [key, setKey] = useState('');

  const { query, handleQuery } = useHandleQueryParams();

  const navigate = useNavigate();

  const [currentDropdown, setCurrentDropdown] = useState('');
  const isActive = () => currentDropdown === 'Open';
  const progressOptionsRef = useRef<HTMLDivElement>(null);

  useOnClickOutside(progressOptionsRef, () => setCurrentDropdown(''));

  const goalRef = query.get('goal_ref') || '';
  const problemStatementRef = query.get('p_s_ref') || '';

  const { setShowModalHandler, currentAccountType, isStudentOrFaculty } =
    useAppContext();

  const { data: problemStatement } = useGetProblemStatement(
    problemStatementRef || '',
    {
      enabled: !!problemStatementRef,
    },
  );
  const next = (res: DataResponse<any>) => {
    (
      [
        'projectName',
        'projectCountries',
        'sdgCategoryRefs',
        'projectDescription',
        'projectImage',
        'projectLevel',
        'problemStatementRef',
        'startDate',
        'endDate',
        'descriptionVideoUrl',
        'documents',
      ] as const
    ).forEach(entry => setValue(entry, undefined));
    setKey(uniqueId());
    if (goalRef) {
      navigate(`/${currentAccountType}/goals?goal_ref=${goalRef}`);
      setShowModalHandler('SelectProjectModal');
      return;
    }
    setShowModalHandler('AddProjectSuccessModal');
    handleQuery({ project_ref: res?.data?.projectRef });
  };
  const {
    handleSubmit,
    watch,
    control,
    register,
    isLoading,
    setValue,
    formState: { errors, isDirty, isValid },
    mutate,
  } = useFormDataAndApiMutateHandler<IAddProjectPayload, any>(
    IAddProjectSchema(isStudentOrFaculty),
    useAddProject,
    {
      next,
      queryKey: GET_PROJECTS_QUERY,
      defaultValues,
    },
  );
  const onSubmit: SubmitHandler<IAddProjectPayload> = data => {
    const { projectLevel, presumedProjectCost, ...rest } = data;

    const newData = {
      ...rest,
      problemStatementRef:
        problemStatementRef || data?.problemStatementRef || undefined,
      goalRef,
      ...(isStudentOrFaculty && { projectLevel }),
      ...(!isStudentOrFaculty && { presumedProjectCost }),
    };
    mutate(newData);
  };

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const { data: problemStatements, isLoading: isLoadingStatements } =
    useGetProblemStatements();

  const { data: sdg, isLoading: isLoadingSdg } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const manipulatedSDGs = useCreateStatementCategoriesOptions(sdg?.data || []);
  const manipulatedStatements = useCreateProblemStatementsArr(
    problemStatements?.data?.problem_statement || [],
  );
  const visibilityOptions = useCreateVisibilityOptions();

  const handleModalToggle = useCallback((value: boolean) => {
    setIsDocumentModalOpen(value);
  }, []);

  const handleFile = (newFiles: File[]) => {
    const oldFiles = watch('documents') || [];

    const mergedFiles = [...oldFiles, ...newFiles];
    setValue('documents', mergedFiles, {
      shouldDirty: true,
      shouldValidate: true,
    });
    handleModalToggle(false);
  };

  const removeFile = (index: number) => {
    const files = watch('documents') || [];
    files.splice(index, 1);
    setValue('documents', files, { shouldDirty: true, shouldValidate: true });
  };

  return (
    <div className="h-full w-full">
      <div className="flex w-full gap-x-2">
        <ArrowBackIcon
          className="cursor-pointer"
          onClick={() =>
            navigate(
              problemStatementRef
                ? `/${currentAccountType}/dashboard-projects/choose-statement`
                : `/${currentAccountType}/dashboard-projects`,
            )
          }
        />
        {problemStatementRef ? (
          <h4 className="max-w-[788px] text-[20px] font-[500] text-black">
            Create a Project for{' '}
            <span className="text-primary">Problem statement</span>:{' '}
            {problemStatement?.data.title}
          </h4>
        ) : (
          <h4 className="max-w-[788px] text-[20px] font-[500] text-black">
            Create a Project
          </h4>
        )}
      </div>
      <div className="pt-4">
        <Form
          onSubmit={handleSubmit(onSubmit)}
          className="w-full max-w-[776px]"
        >
          <div className="">
            <FormInputBox<IAddProjectPayload>
              className={'input mt-2 max-w-full'}
              name="projectName"
              errors={errors}
              labelName="Project Name"
              placeholder="Project Name"
              type="text"
              autoComplete="project-name"
              registerHanlder={() => register('projectName')}
            />
          </div>
          <div className="mt-5">
            <FormSelectBox<IAddProjectPayload>
              key={key}
              labelName="Select Focus Countries (Up to 5)"
              options={Helper.createCountriesOptionsArray(
                countries?.data || { countries: [] },
              )}
              optionsArr={Helper.createCountriesOptionsArray(
                countries?.data || { countries: [] },
              )}
              control={control}
              name="projectCountries"
              errors={errors}
              placeholder="Select Country"
              isLoading={isLoadingCountries}
              isMulti
            />
          </div>
          <div className="mt-5">
            <FormSelectBox<IAddProjectPayload>
              key={key}
              labelName="Select Goals Categories (Up to 2)"
              options={manipulatedSDGs}
              optionsArr={manipulatedSDGs}
              control={control}
              name="sdgCategoryRefs"
              errors={errors}
              placeholder="Goals Categories"
              isLoading={isLoadingSdg}
              isMulti
            />
          </div>
          <div className="mt-5">
            {isStudentOrFaculty ? (
              <FormSelectBox<IAddProjectPayload>
                key={key}
                labelName="Minimum Project Level"
                options={projectLevels}
                optionsArr={projectLevels}
                control={control}
                name="projectLevel"
                errors={errors}
                placeholder="Select Project Level"
              />
            ) : (
              <FormSelectBox<IAddProjectPayload>
                key={key}
                labelName="Presumed Project Cost"
                options={projectCosts}
                optionsArr={projectCosts}
                control={control}
                name="presumedProjectCost"
                errors={errors}
                placeholder="Select Presumed Project Cost"
              />
            )}
          </div>
          <div className="mt-5">
            <p className="">Project Status</p>
            <Controller
              name="projectStatus"
              control={control}
              render={({ field: { onChange, value } }) => (
                <div className="relative mt-2 h-full">
                  <div className="flex h-full items-center">
                    <div
                      onClick={() => setCurrentDropdown('Open')}
                      className={`h-[37px] p-2 ${Helper.getProjectStatusAppearance(
                        Helper.capitalizeString(
                          value || projectProgressStatus.To_Do,
                        ),
                      )} flex cursor-pointer items-center gap-x-2 rounded-[4px]`}
                    >
                      <p className="text-[14px] font-semibold uppercase">
                        {Helper.capitalizeString(
                          value || projectProgressStatus.To_Do,
                        ) === 'To Do'
                          ? 'To-do'
                          : Helper.capitalizeString(
                              value || projectProgressStatus.To_Do,
                            )}
                      </p>
                      <ArrowDownIcon className="h-2 w-2 stroke-black" />
                    </div>
                  </div>
                  {isActive() && (
                    <div
                      ref={progressOptionsRef}
                      className="absolute left-0 top-[calc(46px-30%)] z-[12] min-w-[200px] bg-white shadow-lg"
                    >
                      <ProjectProgressOptions
                        selectedProgress={value}
                        setSelectedStatusProgress={onChange}
                      />
                    </div>
                  )}
                </div>
              )}
            />
          </div>
          <div className="mt-5">
            <FormTextAreaBox<IAddProjectPayload>
              labelName="Project Description"
              className={`input mt-2 min-h-[100px] max-w-full`}
              placeholder="Project Description"
              name="projectDescription"
              errors={errors}
              registerHanlder={() => register('projectDescription')}
              rows={4}
            />
          </div>
          {!problemStatementRef && (
            <div className="mt-5">
              <FormSelectBox<IAddProjectPayload>
                key={key}
                formLabel={
                  <FormLabel
                    name="problemStatementRef"
                    labelName="Add a Problem Statement (optional)"
                  />
                }
                options={manipulatedStatements}
                optionsArr={manipulatedStatements}
                control={control}
                name="problemStatementRef"
                errors={errors}
                placeholder="Select"
                isLoading={isLoadingStatements}
                required={false}
              />
            </div>
          )}
          <div className="mt-5">
            <FormFileInputBox<IAddProjectPayload>
              key={key}
              formLabel={<p className="mb-4">Project Image</p>}
              control={control}
              name="projectImage"
              labelName="Project Image"
              errors={{}}
              autoComplete="organization-department"
              accept="image/*"
            />
          </div>
          <div className="mt-5">
            <FormInputBoxWithForwardRef<IAddProjectPayload>
              formLabel={
                <FormLabel
                  name="descriptionVideoUrl"
                  labelName="Description Video Link - Youtube (Optional)"
                />
              }
              className="input"
              name="descriptionVideoUrl"
              errors={errors}
              labelName=""
              placeholder="Video Link"
              autoComplete="descriptionVideoUrl"
              registerHanlder={() => register('descriptionVideoUrl')}
              required={false}
            />
          </div>
          <div className="mt-5">
            <FormLabel name="comment" labelName="Attach Documents (Optional)" />
            <Button
              className={cn('input group flex gap-2', 'justify-start')}
              type="button"
              onClick={() => handleModalToggle(true)}
            >
              <AttachmentIcon className="stroke-black" />
              <p>Upload Documents</p>
            </Button>
          </div>
          <div className="mt-2 flex flex-wrap gap-2">
            {(watch('documents') || [])?.map((attachment: File, i: number) => (
              <div
                key={i}
                className="w-full max-w-24 border-[2px] border-grayNineTeen"
              >
                <div className="group/first relative rounded-[4px] bg-white after:absolute after:inset-0 after:z-[0] after:duration-500 after:ease-in-out after:content-[''] hover:after:bg-[#00000080] max-md:w-full">
                  {GoalsHelper.isImage(attachment.name) && (
                    <img
                      loading="lazy"
                      src={
                        URL.createObjectURL(attachment) || noImagePlaceholder
                      }
                      className=" h-[50px] w-full max-w-[85px]  self-start overflow-hidden object-cover object-center"
                    />
                  )}
                  {GoalsHelper.isPdf(attachment.name) && (
                    <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                      <PdfIcon className="h-5 w-5" />
                    </div>
                  )}
                  {GoalsHelper.isDoc(attachment.name) && (
                    <div className=" flex h-[50px] w-full max-w-[85px] items-center justify-center">
                      <DocIcon className="h-5 w-5" />
                    </div>
                  )}

                  <div className="absolute right-1 top-1 z-[1] flex items-center gap-1 opacity-0 duration-500 ease-in-out group-hover/first:opacity-100">
                    <span
                      onClick={() => removeFile(i)}
                      className="group relative h-5 w-5 cursor-pointer bg-white p-[2px]"
                    >
                      <DeleteIcon2 className="h-4 w-4" />
                      <div className="absolute -left-[10px] -top-[30px] rounded-lg max-xs:hidden">
                        <ToolTip text="Delete file" />
                      </div>
                    </span>
                  </div>
                </div>
                <p className="py-[2px] text-center text-[10px]">
                  {Helper.truncateAndKeepExtension(attachment?.name, 10)}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-5 flex w-full gap-4">
            <div className="flex-grow">
              <FormDatePickerBox
                control={control}
                labelName="Start Date"
                name="startDate"
                errors={errors}
                className="w-full"
                placeholderText="Start Date"
              />
            </div>
            <div className="flex-grow">
              <FormDatePickerBox
                control={control}
                labelName="End Date"
                name="endDate"
                errors={errors}
                className="w-full"
                minDate={
                  new Date(watch('startDate')) > new Date()
                    ? new Date(watch('startDate'))
                    : new Date()
                }
                placeholderText="End Date"
              />
            </div>
          </div>
          <div className="mt-5">
            <FormSelectBox<IAddProjectPayload>
              key={key}
              labelName="Project Visibility Setting"
              options={visibilityOptions}
              optionsArr={visibilityOptions}
              control={control}
              name="visibility"
              errors={errors}
              placeholder="Select Setting"
            />
          </div>
          <SharedFormButton
            isLoading={isLoading}
            isDirty={isDirty}
            isValid={isValid}
            className=""
          >
            <p className="text-[14px] sm:text-[16px]">Create Project</p>
          </SharedFormButton>
        </Form>
      </div>
      <AddProjectDocumentModal
        isOpen={isDocumentModalOpen}
        onClose={() => handleModalToggle(false)}
        onFileUpload={handleFile}
      />
    </div>
  );
}
