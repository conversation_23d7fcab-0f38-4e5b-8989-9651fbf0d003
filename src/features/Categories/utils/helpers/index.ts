import { TeamlevelUserRoleType } from '../../../../types';
import { Helper } from '../../../../utils/helpers';

export class CategoryHelper extends Helper {
  public static getCategoryStatusFilterApiRequest() {}
  public static createReponseOptions(input: TeamlevelUserRoleType) {
    return {
      label: Helper.capitalizeString(input),
      value: input,
    };
  }
  public static createCategoryOptionsArr = (array: any[]) => {
    return array.map(({ categoryName }) => {
      return { label: categoryName, value: categoryName };
    });
  };
}
