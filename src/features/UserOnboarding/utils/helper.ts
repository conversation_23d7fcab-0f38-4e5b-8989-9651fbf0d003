import { ExtractData, ServerInstitutions } from '@/types';
import { Helper } from '../../../utils/helpers';
import { OnboardingSdgOptionType } from '../types';

export class OnboardingHelper extends Helper {
  public static createCategoryOptionsArr = (array: any[]) => {
    return array.map(({ categoryName, categoryRef }) => {
      return { label: categoryName, value: categoryRef };
    });
  };

  public static createSdgCategoryOptionsArr = (
    array: any[],
  ): OnboardingSdgOptionType[] => {
    return array.map(({ categoryName, categoryRef }) => {
      return { name: categoryName, ref: categoryRef };
    });
  };

  public static getSdgOptionLabel = (option: unknown) => {
    const sdgOption = option as OnboardingSdgOptionType;
    return sdgOption.name;
  };

  public static getSdgOptionValue = (option: unknown) => {
    const sdgOption = option as OnboardingSdgOptionType;
    return sdgOption.ref;
  };
  public static createInstitutionOptionsArrSecondFx(
    argument: ExtractData<ServerInstitutions>,
  ) {
    return argument?.map(({ id, name }) => {
      return {
        label: name,
        value: id,
      };
    });
  }
  public static createInstitutionOptionsMap(
    argument: ExtractData<ServerInstitutions>,
  ) {
    return argument?.reduce(
      (acc, { id, name }) => {
        return {
          ...acc,
          [id]: {
            name,
            id,
          },
        };
      },
      {} as Record<string, { name: string; id: string }>,
    );
  }
}

export const isDevEnvironment = () => {
  const url = typeof window !== 'undefined' ? window.location.href : '';
  return url.includes('localhost');
};
export const isLocalhost = () => {
  const url = typeof window !== 'undefined' ? window.location.href : '';
  return url.includes('localhost');
};
