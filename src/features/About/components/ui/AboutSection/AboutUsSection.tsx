import LazyLoadImageContainer from '../../../../../components/ui/CommonWidget/LazyLoadImageContainer';
import useLazyLoadingHandler from '../../../../../hooks/useLazyLoadingHandler';
import { AboutUsImage } from '../../../assets/images';

export const AboutUsSection = () => {
  const { imgRefs, loaded } = useLazyLoadingHandler();

  return (
    <div className="pb-10 pt-[17px] md:pt-[127px]">
      <div className="flex flex-col gap-[50px] md:flex-row md:gap-[87px]">
        <div className="px-5 md:pl-[117px]">
          <p className="mb-8 mt-3 font-semibold sm:text-[24px] md:leading-10">
            About <span className="gradient-text">Us</span>
          </p>
          <LazyLoadImageContainer
            loaded={loaded}
            className="z-[2] mb-8 flex w-auto flex-grow justify-center md:hidden"
          >
            <img
              loading="lazy"
              ref={element => (imgRefs.current[1] = element!)}
              src={AboutUsImage}
              className={`top-0 z-[2] h-full w-4/5 object-contain object-center ${
                loaded ? 'opacity-100' : 'opacity-0'
              } duration-500 ease-in`}
              alt="Image description"
            />
          </LazyLoadImageContainer>
          <p className=" w-full text-justify text-[16px] leading-[30px] md:max-w-[672px] md:text-[18px]">
            We are pioneering a Global Venture Innovation Networking Ecosystem
            (Global VINE), where students, creatives, researchers,
            entrepreneurs, industry leaders, and (even) AI agents converge to
            solve real-world challenges. Rooted in applied learning, we ensure
            projects don't end as posters or thesis binders but continue until
            they create real impact. By bridging disciplines and sustaining
            collaboration, we transform bold ideas into scalable, world-changing
            solutions.
          </p>
        </div>
        <LazyLoadImageContainer
          loaded={loaded}
          className="z-[2] hidden w-auto flex-grow rounded-[12px] md:block"
        >
          <img
            loading="lazy"
            ref={element => (imgRefs.current[0] = element!)}
            src={AboutUsImage}
            className={`top-0 z-[2] h-full w-full max-w-[500px] rounded-[12px] object-cover object-center ${
              loaded ? 'opacity-100' : 'opacity-0'
            } duration-500 ease-in`}
            alt="Image description"
          />
        </LazyLoadImageContainer>
      </div>
    </div>
  );
};
