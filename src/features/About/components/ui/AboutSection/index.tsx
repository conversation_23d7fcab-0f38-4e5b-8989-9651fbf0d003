import HelmetSEO from '@/components/ui/HelmetSEO';
import { RectangleOneImage } from '../../../../../assets/images';
import { AboutHeroSection } from './AboutHeroSection';
import { AboutUsSection } from './AboutUsSection';
import { AreaOfFocusSection } from './AreaOfFocusSection';
import { GoalsObjectiveSection } from './GoalsObjectiveSection';

export const AboutSection = () => {
  return (
    <>
      <HelmetSEO title="uPIVOTAL" />
      <section
        className="mt-24 bg-gradient-to-r from-orangeThreeFade from-[10%] to-orangeFourFade to-[90%] bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${RectangleOneImage})`,
          backgroundSize: '100% auto',
          backgroundPositionY: '85%',
        }}
      >
        <AboutHeroSection />
        <div className="container_max_width">
          <AboutUsSection />
          <GoalsObjectiveSection />
          <AreaOfFocusSection />
        </div>
      </section>
    </>
  );
};
