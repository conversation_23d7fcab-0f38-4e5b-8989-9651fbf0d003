import LazyLoadImageContainer from '../../../../../components/ui/CommonWidget/LazyLoadImageContainer';
import useLazyLoadingHandler from '../../../../../hooks/useLazyLoadingHandler';
// Import your images here, for now use AboutUsImage as placeholder
import { highlights } from '../../../helpers/data';
export const AboutHighlightsSection = () => {
  const { imgRefs, loaded } = useLazyLoadingHandler();

  return (
    <div className="mx-auto w-[92%] max-w-[1440px] md:w-full md:max-w-none">
      <div className="flex flex-col gap-16 py-12 md:gap-24 md:py-20">
        {highlights.map((section, idx) => (
          <div key={section.title}>
            {/* First item (idx 0) and even indices - Image right, Text left */}
            {idx % 2 === 0 ? (
              <div className="flex flex-col gap-10 md:flex-row md:items-center">
                {/* Text Container - Left on desktop, with padding on smaller screens */}
                <div className="order-2 md:order-1 lg:w-1/2 lg:pl-[8%] xl:pl-[8%] 2xl:pl-[7%]">
                  <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-none xl:max-w-2xl">
                    <h3 className="mb-4 font-semibold leading-tight sm:text-lg">
                      {section.title}
                    </h3>
                    <p className="text-justify text-sm font-normal leading-relaxed text-gray-600 md:text-base">
                      {section.text}
                    </p>
                  </div>
                </div>

                {/* Image Container - Right on desktop, touches screen edge on 1440px+ */}
                <div className="order-1 md:order-2 md:w-1/2">
                  <LazyLoadImageContainer loaded={loaded}>
                    <div className="lg:px-0 lg:pr-0">
                      <div className="mx-auto max-w-lg md:mx-0 md:ml-auto md:max-w-none xl:max-w-2xl">
                        <LazyLoadImageContainer loaded={loaded}>
                          <img
                            loading="lazy"
                            ref={element => (imgRefs.current[idx] = element!)}
                            src={section.image || '/placeholder.svg'}
                            className={`h-64 w-full rounded-lg object-cover transition-opacity duration-500 ease-in md:h-80 lg:h-96 lg:rounded-l-lg lg:rounded-r-none xl:h-[400px] ${
                              loaded ? 'opacity-100' : 'opacity-0'
                            }`}
                            alt={section.alt}
                          />
                        </LazyLoadImageContainer>
                      </div>
                    </div>
                  </LazyLoadImageContainer>
                </div>
              </div>
            ) : (
              /* Odd indices - Image left, Text right */
              <div className="flex flex-col gap-10 md:flex-row md:items-center">
                {/* Image Container - Left on desktop, touches screen edge on 1440px+ */}
                <div className="lg:w-1/2">
                  <LazyLoadImageContainer loaded={loaded}>
                    <div className="lg:px-0 lg:pl-0">
                      <div className="mx-auto max-w-lg lg:mx-0 lg:mr-auto lg:max-w-none xl:max-w-2xl">
                        <LazyLoadImageContainer
                          loaded={loaded}
                          className="flex justify-center md:justify-end"
                        >
                          <img
                            loading="lazy"
                            ref={element => (imgRefs.current[idx] = element!)}
                            src={section.image || '/placeholder.svg'}
                            className={`h-64 w-full rounded-lg object-cover transition-opacity duration-500 ease-in md:h-80 lg:h-96 lg:rounded-l-none lg:rounded-r-lg xl:h-[400px] ${
                              loaded ? 'opacity-100' : 'opacity-0'
                            }`}
                            alt={section.alt}
                          />
                        </LazyLoadImageContainer>
                      </div>
                    </div>
                  </LazyLoadImageContainer>
                </div>

                {/* Text Container - Right on desktop, with padding on smaller screens */}
                <div className="mt-8 lg:mt-0 lg:w-1/2 lg:pr-8 xl:pr-16 2xl:pr-24">
                  <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-none xl:max-w-2xl">
                    <h3 className="mb-4 font-semibold leading-tight sm:text-lg">
                      {section.title}
                    </h3>
                    <p className="text-justify text-sm font-normal leading-relaxed text-gray-600 md:text-base">
                      {section.text}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
