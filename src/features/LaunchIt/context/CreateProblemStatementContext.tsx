import { createContext, useContext, useState, ReactNode } from 'react';
import { CreateProblemStatementFormData } from '../types';

interface CreateProblemStatementContextType {
  formData: Partial<CreateProblemStatementFormData>;
  setFormData: (data: Partial<CreateProblemStatementFormData>) => void;
  updateFormData: (data: Partial<CreateProblemStatementFormData>) => void;
  resetFormData: () => void;
}

const CreateProblemStatementContext = createContext<CreateProblemStatementContextType | undefined>(undefined);

export const useCreateProblemStatementContext = () => {
  const context = useContext(CreateProblemStatementContext);
  if (!context) {
    throw new Error('useCreateProblemStatementContext must be used within a CreateProblemStatementProvider');
  }
  return context;
};

interface CreateProblemStatementProviderProps {
  children: ReactNode;
}

export const CreateProblemStatementProvider = ({ children }: CreateProblemStatementProviderProps) => {
  const [formData, setFormDataState] = useState<Partial<CreateProblemStatementFormData>>({});

  const setFormData = (data: Partial<CreateProblemStatementFormData>) => {
    setFormDataState(data);
  };

  const updateFormData = (data: Partial<CreateProblemStatementFormData>) => {
    setFormDataState(prev => ({ ...prev, ...data }));
  };

  const resetFormData = () => {
    setFormDataState({});
  };

  return (
    <CreateProblemStatementContext.Provider
      value={{
        formData,
        setFormData,
        updateFormData,
        resetFormData,
      }}
    >
      {children}
    </CreateProblemStatementContext.Provider>
  );
};
