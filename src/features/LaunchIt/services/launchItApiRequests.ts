import { usePrivateRequest } from '../../../lib/axios/usePrivateRequest';
import { problemStatements } from '../../../utils/apiServiceControllersRoute';
import { BASE_URL } from '../../../utils/apiUrls';
import { Helper } from '../../../utils/helpers';
import { CreateProblemStatementFormData } from '../types';

export interface LaunchItProblemStatementPayload {
  // title: string;
  // categories: string[];
  // subcategories: string[];
  description: string;
  descriptionVideoUrl?: string;
  descriptionFile?: File | null;
  countries: string[];
  budget: number;
  visibility: string;
  isLaunchIt: boolean;
}

export interface LaunchItProblemStatementResponse {
  status: boolean;
  message: string;
  data: {
    title: string;
    problemStatementRef: string;
    categoryRefList: string[];
    subcategoryRefList: string[];
    descVidUrl: string;
    descDocUrl: string;
    description: string;
    refinedDescription: string;
    createdBy: string;
    createdAt: string;
    createdByName: string;
    approvalStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
    creatorCountry: string;
    countries: string[];
    visibility: 'PUBLIC' | 'PRIVATE';
    budget: number;
    isLaunchIt: boolean;
  };
}

export interface ProposedSolution {
  proposedSolutionRef: string;
  problemStatementRef: string;
  solutionStatement: string;
  problemsSolved: string;
  focusCountries: string[];
  categories: {
    categoryRef: string;
    categoryName: string;
  }[];
  goals: {
    proposedGoalRef: string;
    referenceId: string;
    description: string;
    goalsKeyResults: {
      referenceId: string;
      keyResultDescription: string;
    }[];
  }[];
  focusUNSDGs: string[];
  relevantIndustries: {
    industryRef: string;
    name: string;
  }[];
  constraints: string;
}

export interface ProposedProject {
  referenceId: string;
  name: string;
  description: string;
  focusCountries: string[];
  focusGoalRefs: string[];
  goals: {
    proposedGoalRef: string;
    referenceId: string;
    description: string;
    goalsKeyResults: {
      referenceId: string;
      keyResultDescription: string;
    }[];
  }[];
  minimumProjectLevel: string;
  roleAndHeadCounts: {
    role: string;
    headCount: number;
  }[];
  estimatedCompletionTimeInMonths: number;
  knownCompetitors: string[];
  potentialCollaborators: string[];
}

export interface GenerateProposedProjectsPayload {
  proposedSolutionRef: string;
  projectLevel: string;
}

export interface GenerateProposedSolutionsResponse {
  status: boolean;
  message: string;
  data: ProposedSolution[];
}

export const useCreateLaunchItProblemStatementApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const createProblemStatement = async (
    payload: CreateProblemStatementFormData,
  ): Promise<LaunchItProblemStatementResponse> => {
    // Generate title from first 5-6 words of description
    // const words = payload.description.trim().split(/\s+/);
    // const title = words.slice(0, Math.min(6, words.length)).join(' ');

    // Budget is now a number, so use it directly
    const budgetAmount = payload.budget || 0;

    // Transform form data to API payload
    const apiPayload: LaunchItProblemStatementPayload = {
      // title,
      // categories: ['CTG_BF64RWTW1ERW'], // Other
      // subcategories: ['SBC_Z7ENUOQQD8NT'], // Other
      description: payload.description,
      descriptionVideoUrl: payload.videoLink || undefined,
      descriptionFile: payload.detailedDescriptionFile || undefined,
      countries: payload.focusCountries,
      budget: budgetAmount,
      visibility: payload.status.toUpperCase(),
      isLaunchIt: true,
    };

    // Create FormData for multipart request
    const formData = Helper.createFormData(
      Helper.clearEmptyField({
        ...apiPayload,
        descriptionFile: apiPayload.descriptionFile,
      }),
    );

    const res = await axiosInstance.current?.post(
      `${problemStatements}/launch-it`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      },
    );

    return res?.data;
  };

  return createProblemStatement;
};

export const useGenerateProposedSolutionsApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const generateProposedSolutions = async (
    problemStatementRef: string,
  ): Promise<GenerateProposedSolutionsResponse> => {
    const res = await axiosInstance.current?.post(
      `/upivotal-edu-service/proposed-solutions/${problemStatementRef}`,
    );

    return res?.data;
  };

  return generateProposedSolutions;
};

export const useGenerateProposedProjectsApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const generateProposedProjects = async (
    payload: GenerateProposedProjectsPayload,
  ): Promise<{ status: boolean; message: string; data: ProposedProject[] }> => {
    const res = await axiosInstance.current?.post(
      `/upivotal-edu-service/proposed-solutions/project`,
      payload,
    );

    return res?.data;
  };

  return generateProposedProjects;
};

export const useGetProposedSolutionApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getProposedSolution = async (
    proposedSolutionRef: string,
  ): Promise<ProposedSolution> => {
    const res = await axiosInstance.current?.get(
      `/upivotal-edu-service/proposed-solutions/${proposedSolutionRef}`,
    );

    return res?.data?.data;
  };

  return getProposedSolution;
};

export const useGetProposedProjectApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getProposedProject = async (
    proposedProjectRef: string,
  ): Promise<ProposedProject> => {
    const res = await axiosInstance.current?.get(
      `/upivotal-edu-service/proposed-solutions/project/${proposedProjectRef}`,
    );

    return res?.data?.data;
  };

  return getProposedProject;
};

export const useGetProblemStatementApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const getProblemStatement = async (
    problemStatementRef: string,
  ): Promise<LaunchItProblemStatementResponse> => {
    const res = await axiosInstance.current?.get(
      `/upivotal-edu-service/problem-statements/public/${problemStatementRef}`,
    );

    return res?.data?.data;
  };

  return getProblemStatement;
};
