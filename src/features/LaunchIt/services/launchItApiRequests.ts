import { usePrivateRequest } from '../../../lib/axios/usePrivateRequest';
import { problemStatements } from '../../../utils/apiServiceControllersRoute';
import { BASE_URL } from '../../../utils/apiUrls';
import { Helper } from '../../../utils/helpers';
import { CreateProblemStatementFormData } from '../types';

export interface LaunchItProblemStatementPayload {
  title: string;
  categories: string[];
  subcategories: string[];
  description: string;
  descriptionVideoUrl?: string;
  descriptionFile?: File | null;
  countries: string[];
  budget: number;
  visibility: string;
  isLaunchIt: boolean;
}

export interface LaunchItProblemStatementResponse {
  status: boolean;
  message: string;
  data: {
    title: string;
    problemStatementRef: string;
    categoryRefList: string[];
    subcategoryRefList: string[];
    descVidUrl: string;
    descDocUrl: string;
    description: string;
    refinedDescription: string;
    createdBy: string;
    createdAt: string;
    createdByName: string;
    approvalStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
    creatorCountry: string;
    countries: string[];
    visibility: 'PUBLIC' | 'PRIVATE';
    budget: number;
    isLaunchIt: boolean;
  };
}

export const useCreateLaunchItProblemStatementApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const createProblemStatement = async (
    payload: CreateProblemStatementFormData,
  ): Promise<LaunchItProblemStatementResponse> => {
    // Generate title from first 5-6 words of description
    const words = payload.description.trim().split(/\s+/);
    const title = words.slice(0, Math.min(6, words.length)).join(' ');

    // Extract budget amount (take lower limit)
    const budgetMatch = payload.budget.match(/\$?([\d,]+)/);
    const budgetAmount = budgetMatch ? parseInt(budgetMatch[1].replace(/,/g, ''), 10) : 0;

    // Transform form data to API payload
    const apiPayload: LaunchItProblemStatementPayload = {
      title,
      categories: ['CTG_BF64RWTW1ERW'], // Other
      subcategories: ['SBC_Z7ENUOQQD8NT'], // Other
      description: payload.description,
      descriptionVideoUrl: payload.videoLink || undefined,
      descriptionFile: payload.detailedDescriptionFile || undefined,
      countries: payload.focusCountries,
      budget: budgetAmount,
      visibility: payload.status.toUpperCase(),
      isLaunchIt: true,
    };

    // Create FormData for multipart request
    const formData = Helper.createFormData(
      Helper.clearEmptyField({
        ...apiPayload,
        descriptionFile: apiPayload.descriptionFile,
      }),
    );

    const res = await axiosInstance.current?.post(
      `${problemStatements}/launch-it`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      },
    );

    return res?.data;
  };

  return createProblemStatement;
};
