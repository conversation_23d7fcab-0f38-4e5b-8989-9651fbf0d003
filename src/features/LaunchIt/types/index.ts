export interface CreateProblemStatementFormData {
  description: string;
  detailedDescriptionFile?: File | null;
  focusCountries: string[];
  budget: string;
  status: 'private' | 'public';
  videoLink: string;
}

export interface BudgetOption {
  value: string;
  label: string;
}

export interface ProblemStatementResponse {
  id: string;
  title: string;
  description: string;
  focusCountries: Array<{
    code: string;
    name: string;
  }>;
  focusIndustries: Array<{
    id: string;
    name: string;
  }>;
  goalsCategories: Array<{
    id: string;
    name: string;
  }>;
  budget: string;
  status: 'private' | 'public';
  videoLink: string;
  attachedFiles: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  createdBy: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  createdAt: string;
}

export interface BudgetOption {
  value: string;
  label: string;
}

export interface CountryOption {
  value: string;
  label: string;
}

export interface IndustryOption {
  value: string;
  label: string;
}

export interface GoalsCategoryOption {
  value: string;
  label: string;
}
