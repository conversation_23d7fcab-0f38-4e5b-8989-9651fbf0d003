import { ArrowRight } from 'lucide-react';
import React from 'react';

interface CardOptionProps {
  header: string;
  title: string;
  description: string;
  buttonLabel: string;
  backgroundImage: string;
  onClick?: () => void;
  href?: string;
}

const CardOption: React.FC<CardOptionProps> = ({
  header,
  title,
  description,
  buttonLabel,
  backgroundImage,
  onClick,
  href,
}) => {
  const cardStyle = {
    backgroundImage: `url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  };

  const content = (
    <div
      className="relative h-[350px] w-[220px] rounded-xl px-4 text-white shadow-md transition-all duration-300 ease-in-out hover:scale-105"
      style={cardStyle}
    >
      <div className="mt-28">
        <p className="text-xs uppercase">{header}</p>
        <h3 className="mt-2 text-lg font-bold">{title}</h3>
        <p className="mt-4 text-xs font-medium">{description}</p>
      </div>

      <div className="absolute bottom-10 left-4 right-4">
        <button
          onClick={onClick}
          className="text-grayTwentyTwo flex h-9 w-full items-center justify-center gap-2 rounded-lg bg-white py-1.5 text-sm font-medium transition-all duration-300 ease-in-out hover:bg-primary hover:text-white"
        >
          {buttonLabel} <ArrowRight size={24} className="-mt-0.5" />
        </button>
      </div>
    </div>
  );

  return href ? <a href={href}>{content}</a> : content;
};

export default CardOption;
