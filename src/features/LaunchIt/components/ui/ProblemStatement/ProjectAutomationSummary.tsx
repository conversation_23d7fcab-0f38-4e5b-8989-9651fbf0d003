import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { useAppContext } from '@/context/event/AppEventContext';
import { useCustomToast } from '@/hooks/useToast';
import { useGenerateProjectPlan } from '../../../hooks/apiQueryHooks/launchItQueryHooks';
import { AutomationSummaryResponse } from '../../../services/launchItApiRequests';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

export default function ProjectAutomationSummary() {
  const location = useLocation();
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();
  const { successToast, errorToast } = useCustomToast();

  // Get the automation summary data from location state
  const automationData: AutomationSummaryResponse['data'] =
    location.state?.automationData;

  // Project Plan API integration
  const { mutate: generateProjectPlan, isPending: isGeneratingProjectPlan } =
    useGenerateProjectPlan({
      onSuccess: () => {
        successToast(
          'Generating Project Plan in background. You will be notified when plan is ready.',
        );
        setTimeout(() => {
          navigate(`/${currentAccountType}/dashboard-overview`);
        }, 2500);
      },
      onError: (error: any) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to generate project plan. Please try again.',
        );
      },
    });

  const handleBack = () => {
    navigate(-1);
  };

  const handleGenerateProjectPlan = () => {
    if (!automationData?.projectReferenceId) {
      errorToast('Project reference not found. Please try again.');
      return;
    }

    // Create start date as current time plus one hour
    const startDate = new Date();
    startDate.setHours(startDate.getHours() + 1);

    generateProjectPlan({
      proposedProjectReferenceId: automationData.projectReferenceId,
      startDate: startDate.toISOString(),
    });
  };

  if (!automationData) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="mx-auto max-w-4xl">
          <div className="flex items-center gap-2 px-3.5">
            <button
              onClick={handleBack}
              className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
            >
              <ArrowLeft size={24} className="-mt-0.5 text-primary" />
              <span className="text-lg font-medium">
                Project Automation Summary
              </span>
            </button>
          </div>
          <div className="py-12 text-center">
            <p className="text-gray-600">
              No automation summary data available.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">
              Project Automation Summary
            </span>
          </button>
        </div>

        {/* Action Button */}
        <div className="">
          <Button
            onClick={handleGenerateProjectPlan}
            disabled={isGeneratingProjectPlan}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:border-none disabled:bg-darkOrangeTwo sm:w-auto"
          >
            {isGeneratingProjectPlan ? (
              <div className="flex items-center gap-2">
                <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                <span>Generating Plan...</span>
              </div>
            ) : (
              'Generate Project Plan →'
            )}
          </Button>
        </div>
      </div>

      <div className="mx-auto max-w-4xl p-6">
        {/* Main Content */}
        <div className="space-y-6">
          {/* Project Overview */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
            <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
              {/* <Zap className="text-primary" size={20} /> */}
              Project Overview
            </h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Project Level
                </p>
                <p className="text-lg font-semibold text-gray-900">
                  {automationData.projectLevel}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Estimated Human Duration
                </p>
                <p className="text-lg font-semibold text-gray-900">
                  {automationData.estimatedHumanDuration}
                </p>
              </div>
            </div>
          </div>

          {/* uPivotal Tool Equivalents */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
            <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
              {/* <CheckCircle className="text-green-600" size={20} /> */}
              uPivotal Tool Equivalents
            </h2>
            <div className="space-y-3">
              {Object.entries(automationData.uPivotalToolEquivalents).map(
                ([tool, description]) => (
                  <div key={tool} className="flex items-start gap-3">
                    <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-primary"></div>
                    <div>
                      <p className="font-medium capitalize text-gray-900">
                        {/* {tool.replace(/([A-Z])/g, ' $1').trim()} */}
                        {tool}
                      </p>
                      <p className="text-gray-600">{description}</p>
                    </div>
                  </div>
                ),
              )}
            </div>
          </div>

          {/* Planning Metrics */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
            <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
              {/* <Clock className="text-blue-600" size={20} /> */}
              Estimated Planning Metrics
            </h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Estimated Task Count
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {automationData.estimatedPlanningMetrics.estimatedTaskCount}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Task Generation Time
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    automationData.estimatedPlanningMetrics
                      .taskGenerationTimeMinutes
                  }{' '}
                  min
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Team Skill Level
                </p>
                <p className="text-lg font-semibold text-gray-900">
                  {
                    automationData.estimatedPlanningMetrics
                      .assumedTeamSkillLevel
                  }
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Human Only Duration
                </p>
                <p className="text-lg font-semibold text-gray-900">
                  {automationData.estimatedPlanningMetrics.humanOnlyDuration}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Pivo Assisted Duration
                </p>
                <p className="text-green-600 text-lg font-semibold">
                  {automationData.estimatedPlanningMetrics.pivoAssistedDuration}
                </p>
              </div>
            </div>
          </div>

          {/* Non-Automatable Tasks */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
            <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
              {/* <AlertCircle className="text-amber-600" size={20} /> */}
              Non-Automatable Tasks
            </h2>
            <div className="space-y-2">
              {automationData.nonAutomatableTasks.map((task, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="h-2 w-2 flex-shrink-0 rounded-full bg-amber-500"></div>
                  <p className="text-gray-700">{task}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Automation Summary */}
          <div className="rounded-lg border border-primary/20 bg-gradient-to-r from-primary/5 to-blue-50 p-6">
            <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
              {/* <Users className="text-primary" size={20} /> */}
              Automation Summary
            </h2>
            <p className="leading-relaxed text-gray-700">
              {automationData.automationSummary}
            </p>
          </div>

          {/* Action Button */}
          <div className="flex justify-end pt-6">
            <Button
              onClick={handleGenerateProjectPlan}
              disabled={isGeneratingProjectPlan}
              className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:border-none disabled:bg-darkOrangeTwo sm:w-auto"
            >
              {isGeneratingProjectPlan ? (
                <div className="flex items-center gap-2">
                  <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                  <span>Generating Plan...</span>
                </div>
              ) : (
                'Generate Project Plan →'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
