import { useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { ProblemStatementResponse } from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import { Edit2, Save } from 'lucide-react';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useGetSDG, useGetSDGs } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { useCreateStatementCategoriesOptions } from '@/hooks/useCreateStatementCategoriesOptions';
import { useCreateStatementSubCategoriesOptions } from '@/hooks/useCreateStatementSubCategoriesOptions';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(data.title);
  const [editedDescription, setEditedDescription] = useState(data.description);
  const [editedCountries, ] = useState(
    data.focusCountries.map(country => country.name),
  );
  const [editedCategories, ] = useState(
    data.goalsCategories.map(category => category.id),
  );
  const [editedSubcategories, ] = useState<string[]>([]);

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const countryOptions = (countries?.data || []).map((country: any) => ({
    label: country.name,
    value: country.name,
  }));

  const { data: categories, isLoading: isLoadingCategories } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const {
    control,
    formState: { errors },
    getValues,
  } = useForm<{
    focusCountries: string[];
    categories: string[];
    subcategories: string[];
  }>({
    defaultValues: {
      focusCountries: editedCountries,
      categories: editedCategories,
      subcategories: editedSubcategories,
    },
  });

  const { data: category, isFetching: isFetchingCategory } = useGetSDG(
    getValues('categories')?.[0] || '',
    {
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
      enabled: !!getValues('categories')?.[0],
    },
  );

  useWatch({
    control,
    defaultValue: {
      categories: [],
    },
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // Here you would typically save the changes to the backend
    console.log('Saving changes:', {
      title: editedTitle,
      description: editedDescription,
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedTitle(data.title);
    setEditedDescription(data.description);
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Edit Button */}
      <div className="flex justify-end">
        {!isEditing ? (
          <Button
            onClick={handleEdit}
            className="flex items-center gap-2 border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
          >
            <Edit2 size={16} />
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              onClick={handleCancel}
              className="border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="flex items-center gap-2 border border-primary bg-primary px-4 py-2 text-white hover:bg-primary/90"
            >
              <Save size={16} />
              Save
            </Button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        {!isEditing ? (
          <h1 className="mb-4 text-2xl font-bold text-gray-900">
            {data.title}
          </h1>
        ) : (
          <input
            type="text"
            value={editedTitle}
            onChange={e => setEditedTitle(e.target.value)}
            className="mb-4 w-full rounded-md border border-gray-300 px-3 py-2 text-2xl font-bold text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Description */}
      <div>
        <h2 className="mb-3 text-lg font-semibold text-gray-800">
          Description
        </h2>
        {!isEditing ? (
          <div className="prose max-w-none">
            <p className="whitespace-pre-wrap leading-relaxed text-gray-700">
              {data.description}
            </p>
          </div>
        ) : (
          <textarea
            value={editedDescription}
            onChange={e => setEditedDescription(e.target.value)}
            rows={6}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Focus Countries */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Focus Countries
        </h3>
        {!isEditing ? (
          <div className="flex flex-wrap gap-2">
            {data.focusCountries.map(country => (
              <span
                key={country.code}
                className="inline-flex items-center rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-sm text-blue-800"
              >
                🇳🇬 {country.name}
              </span>
            ))}
          </div>
        ) : (
          <FormSelectBox
            name="focusCountries"
            labelName=""
            placeholder="Select Focus Countries"
            control={control}
            errors={errors}
            options={countryOptions}
            optionsArr={countryOptions}
            isMulti
            isLoading={isLoadingCountries}
          />
        )}
      </div>

      {/* Focus Industries */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Focus Industries
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.focusIndustries.map(industry => (
            <span
              key={industry.id}
              className="bg-green-100 text-green-800 border-green-200 inline-flex items-center rounded-full border px-3 py-1 text-sm"
            >
              {industry.name}
            </span>
          ))}
        </div>
      </div>

      {/* Goals Categories */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Goals Categories
        </h3>
        {!isEditing ? (
          <div className="flex flex-wrap gap-2">
            {data.goalsCategories.map(goal => (
              <span
                key={goal.id}
                className="bg-purple-100 text-purple-800 border-purple-200 inline-flex items-center rounded-full border px-3 py-1 text-sm"
              >
                {goal.name}
              </span>
            ))}
          </div>
        ) : (
          <FormSelectBox
            name="categories"
            labelName=""
            placeholder="Select Goals Categories (Up to 2)"
            control={control}
            errors={errors}
            options={
              useCreateStatementCategoriesOptions(categories?.data || []) || []
            }
            optionsArr={
              useCreateStatementCategoriesOptions(categories?.data || []) || []
            }
            isMulti
            isLoading={isLoadingCategories}
          />
        )}
      </div>

      {/* Goals Subcategories */}
      {isEditing && (
        <div className="relative">
          <FormSelectBox
            name="subcategories"
            labelName="Goals SubCategories"
            placeholder="Select Subcategory"
            control={control}
            errors={errors}
            options={
              useCreateStatementSubCategoriesOptions(
                category?.data.subcategories || [],
              ) || []
            }
            optionsArr={
              useCreateStatementSubCategoriesOptions(
                category?.data.subcategories || [],
              ) || []
            }
            isMulti
            isLoading={isFetchingCategory}
          />
          {!getValues('categories')?.[0] && (
            <div className="absolute inset-0 rounded-md bg-gray-100 bg-opacity-50" />
          )}
        </div>
      )}

      {/* Budget */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">Budget</h3>
        <p className="text-gray-700">{data.budget}</p>
      </div>

      {/* Status */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">Status</h3>
        <span
          className={`inline-flex items-center rounded-full px-3 py-1 text-sm ${
            data.status === 'public'
              ? 'bg-green-100 text-green-800 border-green-200 border'
              : 'border border-gray-200 bg-gray-100 text-gray-800'
          }`}
        >
          {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
        </span>
      </div>
    </div>
  );
}
