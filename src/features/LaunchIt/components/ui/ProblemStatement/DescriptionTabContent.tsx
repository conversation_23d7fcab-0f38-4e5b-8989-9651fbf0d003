import { useState, useEffect, useMemo, useRef } from 'react';
import { useForm, useWatch, Controller } from 'react-hook-form';
import {
  ProblemStatementResponse,
  CountryOption,
  CategoryOption,
  SubcategoryOption,
  IndustryOption,
} from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import { PencilLine, X, ChevronDown } from 'lucide-react';
import FormSelectBox from '@/components/forms/FormSelectBox';
import FormLabel from '@/components/forms/FormLabel';
import FormSelect from '@/components/forms/FormSelect';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import {
  useGetSDG,
  useGetSDGs,
  useGetIndustryCategories,
} from '@/hooks/apiQueryHooks/eduQueryHooks';
import { useCreateStatementCategoriesOptions } from '@/hooks/useCreateStatementCategoriesOptions';
import { useCreateStatementSubCategoriesOptions } from '@/hooks/useCreateStatementSubCategoriesOptions';
import { Helper } from '@/utils/helpers';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { countryFlags } from '@/utils/helpers/country-flags-data';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(data.title);
  const [editedDescription, setEditedDescription] = useState(data.description);

  // Focus Countries state
  const [selectedCountries, setSelectedCountries] = useState<
    Array<{ code: string; name: string }>
  >(data.focusCountries || []);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [countryError, setCountryError] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { data: countries } = useGetCountries();
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );

  const { data: categories, isLoading: isLoadingCategories } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const { data: industries, isLoading: isLoadingIndustries } =
    useGetIndustryCategories({
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
    });

  // Create options arrays
  const categoryOptions = useCreateStatementCategoriesOptions(
    categories?.data || [],
  );
  const industryOptions = GoalsHelper.createCategoryOptionsArr(
    industries?.data || [],
  );

  // Preselect values from API response data - memoized to prevent infinite re-renders
  const preselectedCountries = useMemo(
    () =>
      countryOptions.filter(option =>
        data.focusCountries.some(country => country.name === option.value),
      ),
    [countryOptions, data.focusCountries],
  );

  // For categories, match the categoryRef from API response to the actual category options
  const preselectedCategories = useMemo(
    () =>
      categoryOptions.filter(option =>
        data.goalsCategories.some(category => category.id === option.value),
      ),
    [categoryOptions, data.goalsCategories],
  );

  const preselectedIndustries = useMemo(
    () =>
      industryOptions.filter(option =>
        data.focusIndustries.some(industry => industry.id === option.value),
      ),
    [industryOptions, data.focusIndustries],
  );

  interface DescriptionFormData {
    focusCountries?: CountryOption[];
    categories?: CategoryOption[];
    subcategories?: SubcategoryOption[];
    industryCategoryRefs?: IndustryOption[];
  }

  const {
    control,
    formState: { errors },
    getValues,
    reset,
  } = useForm<DescriptionFormData>({
    defaultValues: {
      focusCountries: [],
      categories: [],
      subcategories: [],
      industryCategoryRefs: [],
    },
  });

  // Update form when preselected values are available
  useEffect(() => {
    if (
      preselectedCountries.length > 0 ||
      preselectedCategories.length > 0 ||
      preselectedIndustries.length > 0
    ) {
      reset({
        focusCountries: preselectedCountries,
        categories: preselectedCategories,
        subcategories: [], // Will be set separately when subcategory data is available
        industryCategoryRefs: preselectedIndustries,
      });
    }
  }, [
    preselectedCountries,
    preselectedCategories,
    preselectedIndustries,
    reset,
  ]);

  // Watch for category changes to fetch subcategories
  const selectedCategories = useWatch({ control, name: 'categories' });
  const firstCategoryRef = selectedCategories?.[0]?.value || '';

  const { data: category, isFetching: isFetchingCategory } = useGetSDG(
    firstCategoryRef,
    {
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
      enabled: !!firstCategoryRef,
    },
  );

  // Create subcategory options from the selected category
  const subcategoryOptions = useCreateStatementSubCategoriesOptions(
    category?.data?.subcategories || [],
  );
  const preselectedSubcategories = useMemo(
    () =>
      subcategoryOptions?.filter(option =>
        data.goalsSubcategories?.some(
          subcategory => subcategory.id === option.value,
        ),
      ) || [],
    [subcategoryOptions, data.goalsSubcategories],
  );

  // Update subcategories when subcategory data becomes available
  useEffect(() => {
    if (preselectedSubcategories.length > 0) {
      reset(prevValues => ({
        ...prevValues,
        subcategories: preselectedSubcategories,
      }));
    }
  }, [preselectedSubcategories, reset]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // TODO: Implement API call to save changes
    console.log('Saving changes:', {
      title: editedTitle,
      description: editedDescription,
    });
    setIsEditing(false);
  };

  // Focus Countries helper functions
  const handleAddCountry = (countryName: string) => {
    setCountryError('');

    // Check if country is already selected
    if (selectedCountries.some(country => country.name === countryName)) {
      setCountryError('Country is already selected');
      return;
    }

    // Check maximum limit
    if (selectedCountries.length >= 5) {
      setCountryError('Maximum 5 countries can be selected');
      return;
    }

    // Find the country data from the countries data
    const countryData = countries?.data?.countries?.find(
      c => c.shortName === countryName,
    );
    if (countryData && countryData.shortName && countryData.countryCode) {
      setSelectedCountries(prev => [
        ...prev,
        { code: countryData.countryCode!, name: countryData.shortName! },
      ]);
    }
    setShowCountryDropdown(false);
  };

  const handleRemoveCountry = (countryName: string) => {
    setSelectedCountries(prev =>
      prev.filter(country => country.name !== countryName),
    );
    setCountryError('');
  };

  // Get available countries for dropdown (excluding already selected ones)
  const availableCountries = useMemo(() => {
    if (!countries?.data?.countries) return [];
    return countries.data.countries.filter(
      country =>
        country.shortName &&
        country.countryCode &&
        !selectedCountries.some(
          selected => selected.name === country.shortName,
        ),
    );
  }, [countries?.data?.countries, selectedCountries]);

  // Validate countries selection
  useEffect(() => {
    if (selectedCountries.length === 0) {
      setCountryError('At least one country must be selected');
    } else {
      setCountryError('');
    }
  }, [selectedCountries]);

  // Click outside handler for dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowCountryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* Edit Button */}
      <div className="-mt-[72px] flex justify-end">
        {!isEditing ? (
          <Button
            onClick={handleEdit}
            className="flex items-center gap-2 bg-grayNine px-4 py-2 text-white hover:bg-primary"
          >
            Edit
            <PencilLine size={20} className="-mt-0.5" />
          </Button>
        ) : (
          <div className="">
            <Button
              onClick={handleSave}
              className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        {!isEditing ? (
          <p className="mb-4 rounded-lg border border-grayFifteen bg-white px-4 py-3 text-xl font-semibold text-gray-900">
            {data.title}
          </p>
        ) : (
          <input
            type="text"
            value={editedTitle}
            onChange={e => setEditedTitle(e.target.value)}
            className="mb-4 w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-semibold text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Description */}
      <div>
        {!isEditing ? (
          <div className="prose max-w-none bg-white">
            <p className="whitespace-pre-wrap rounded-lg border border-grayFifteen px-4 py-3 text-gray-700">
              {data.description}
            </p>
          </div>
        ) : (
          <textarea
            value={editedDescription}
            onChange={e => setEditedDescription(e.target.value)}
            rows={6}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Focus Countries */}
      <div className="flex items-center gap-2">
        <div className="bg-peachOne flex items-center justify-between rounded-xl p-2">
          <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
            Focus Countries
          </div>
        </div>

        {/* Country Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => {
              if (availableCountries.length > 0) {
                setShowCountryDropdown(!showCountryDropdown);
              }
            }}
            className={`flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 ${
              selectedCountries.length >= 5
                ? 'cursor-not-allowed bg-gray-100'
                : 'bg-peachOne'
            } px-3 py-2 text-left text-sm`}
          >
            <div className="flex flex-wrap gap-2">
              {selectedCountries.map(country => (
                <div
                  key={country.code}
                  className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                >
                  <span className="text-lg">
                    {countryFlags[country.name] || '🏳️'}
                  </span>
                  <span className="text-sm">{country.name}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveCountry(country.name)}
                    className="ml-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-black text-white hover:bg-red-600"
                  >
                    <X size={8} />
                  </button>
                </div>
              ))}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </button>

          {showCountryDropdown && availableCountries.length > 0 && (
            <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-300 bg-white shadow-lg">
              {availableCountries.map(country => (
                <button
                  key={country.countryCode}
                  type="button"
                  onClick={() => handleAddCountry(country.shortName!)}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <span className="text-lg">
                    {countryFlags[country.shortName!] || '🏳️'}
                  </span>
                  <span>{country.shortName}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      {/* Error Message */}
      {countryError && <p className="text-sm text-red-600">{countryError}</p>}

      {/* Focus Industries */}
      <div>
        <FormSelectBox
          options={industryOptions}
          optionsArr={industryOptions}
          control={control}
          name={'industryCategoryRefs' as never}
          errors={errors}
          labelName="Focus Industries"
          placeholder="Select Industries"
          isClearable={false}
          isMulti
          isLoading={isLoadingIndustries}
          required={false}
        />
      </div>

      {/* Goals Categories */}
      <div>
        <div>
          <FormLabel
            name="categories"
            labelName="Goals Categories"
            className="after:text-[16px] after:text-primary after:content-['*']"
          />
          <Controller
            name="categories"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <FormSelect
                id="categories"
                value={
                  value && Array.isArray(value)
                    ? categoryOptions.filter(x =>
                        (value as any).includes(x.value),
                      )
                    : []
                }
                onChange={(option: any) => {
                  Array.isArray(option)
                    ? onChange(option.map(({ value }) => value))
                    : onChange(option.value);
                }}
                onBlur={onBlur}
                name="categories"
                options={categoryOptions}
                placeholder="Select Goals Categories"
                isMulti
                isLoading={isLoadingCategories}
                required={false}
              />
            )}
          />
          {errors.categories && (
            <p className="mt-2 text-[12px] text-red-800 sm:text-[14px]">
              {errors.categories?.message as string}
            </p>
          )}
        </div>
      </div>

      {/* Goals Subcategories */}
      <div className="relative">
        <FormSelectBox
          name={'subcategories' as never}
          labelName="Goals SubCategories"
          placeholder="Select Subcategories"
          control={control}
          errors={errors}
          options={subcategoryOptions || []}
          optionsArr={subcategoryOptions || []}
          defaultValue={preselectedSubcategories}
          isMulti
          isLoading={isFetchingCategory}
          required={false}
        />
        {!getValues('categories')?.[0] && (
          <div className="absolute inset-0 rounded-md bg-gray-100 bg-opacity-50" />
        )}
      </div>
    </div>
  );
}
