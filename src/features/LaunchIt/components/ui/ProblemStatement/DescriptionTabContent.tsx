import Button from '@/components/ui/ButtonComponent';
import { ProblemStatementResponse } from '../../../types';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  const handleSaveForLater = () => {
    // Implementation for saving for later
    console.log('Save for later clicked');
  };

  const handleGenerateProposedSolutions = () => {
    // Implementation for generating proposed solutions
    console.log('Generate proposed solutions clicked');
  };

  return (
    <div className="space-y-6">
      {/* Created By Section */}
      <div className="mb-6 flex items-center gap-3">
        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
          {data.createdBy.avatar ? (
            <img
              src={data.createdBy.avatar}
              alt={`${data.createdBy.firstName} ${data.createdBy.lastName}`}
              className="h-full w-full rounded-full object-cover"
            />
          ) : (
            <span className="text-sm font-medium text-gray-600">
              {data.createdBy.firstName.charAt(0)}
              {data.createdBy.lastName.charAt(0)}
            </span>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Created by:</p>
          <p className="font-medium">
            {data.createdBy.firstName} {data.createdBy.lastName}
          </p>
        </div>
      </div>

      {/* Title */}
      <div>
        <h1 className="mb-4 text-2xl font-bold text-gray-900">{data.title}</h1>
      </div>

      {/* Description */}
      <div>
        <h2 className="mb-3 text-lg font-semibold text-gray-800">
          Description
        </h2>
        <div className="prose max-w-none">
          <p className="whitespace-pre-wrap leading-relaxed text-gray-700">
            {data.description}
          </p>
        </div>
      </div>

      {/* Focus Countries */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Focus Countries
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.focusCountries.map(country => (
            <span
              key={country.code}
              className="inline-flex items-center rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-sm text-blue-800"
            >
              🇳🇬 {country.name}
            </span>
          ))}
        </div>
      </div>

      {/* Focus Industries */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Focus Industries
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.focusIndustries.map(industry => (
            <span
              key={industry.id}
              className="bg-green-100 text-green-800 border-green-200 inline-flex items-center rounded-full border px-3 py-1 text-sm"
            >
              {industry.name}
            </span>
          ))}
        </div>
      </div>

      {/* Goals Categories */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Goals Categories
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.goalsCategories.map(goal => (
            <span
              key={goal.id}
              className="bg-purple-100 text-purple-800 border-purple-200 inline-flex items-center rounded-full border px-3 py-1 text-sm"
            >
              {goal.name}
            </span>
          ))}
        </div>
      </div>

      {/* Budget */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">Budget</h3>
        <p className="text-gray-700">{data.budget}</p>
      </div>

      {/* Status */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">Status</h3>
        <span
          className={`inline-flex items-center rounded-full px-3 py-1 text-sm ${
            data.status === 'public'
              ? 'bg-green-100 text-green-800 border-green-200 border'
              : 'border border-gray-200 bg-gray-100 text-gray-800'
          }`}
        >
          {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
        </span>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col gap-4 pt-6 sm:flex-row">
        <Button
          onClick={handleSaveForLater}
          className="w-full border border-gray-300 bg-white px-6 py-3 text-gray-700 hover:bg-gray-50 sm:w-auto"
        >
          Save For Later
        </Button>
        <Button
          onClick={handleGenerateProposedSolutions}
          className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/90 sm:w-auto"
        >
          Generate Proposed Solutions →
        </Button>
      </div>
    </div>
  );
}
