import Button from '@/components/ui/ButtonComponent';
import { ProblemStatementResponse } from '../../../types';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({ data }: DescriptionTabContentProps) {
  const handleSaveForLater = () => {
    // Implementation for saving for later
    console.log('Save for later clicked');
  };

  const handleGenerateProposedSolutions = () => {
    // Implementation for generating proposed solutions
    console.log('Generate proposed solutions clicked');
  };

  return (
    <div className="space-y-6">
      {/* Created By Section */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
          {data.createdBy.avatar ? (
            <img 
              src={data.createdBy.avatar} 
              alt={`${data.createdBy.firstName} ${data.createdBy.lastName}`}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <span className="text-sm font-medium text-gray-600">
              {data.createdBy.firstName.charAt(0)}{data.createdBy.lastName.charAt(0)}
            </span>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Created by:</p>
          <p className="font-medium">{data.createdBy.firstName} {data.createdBy.lastName}</p>
        </div>
      </div>

      {/* Title */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">{data.title}</h1>
      </div>

      {/* Description */}
      <div>
        <h2 className="text-lg font-semibold text-gray-800 mb-3">Description</h2>
        <div className="prose max-w-none">
          <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{data.description}</p>
        </div>
      </div>

      {/* Focus Countries */}
      <div>
        <h3 className="text-md font-semibold text-gray-800 mb-3">Focus Countries</h3>
        <div className="flex flex-wrap gap-2">
          {data.focusCountries.map((country) => (
            <span
              key={country.code}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200"
            >
              🇳🇬 {country.name}
            </span>
          ))}
        </div>
      </div>

      {/* Focus Industries */}
      <div>
        <h3 className="text-md font-semibold text-gray-800 mb-3">Focus Industries</h3>
        <div className="flex flex-wrap gap-2">
          {data.focusIndustries.map((industry) => (
            <span
              key={industry.id}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 border border-green-200"
            >
              {industry.name}
            </span>
          ))}
        </div>
      </div>

      {/* Goals Categories */}
      <div>
        <h3 className="text-md font-semibold text-gray-800 mb-3">Goals Categories</h3>
        <div className="flex flex-wrap gap-2">
          {data.goalsCategories.map((goal) => (
            <span
              key={goal.id}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800 border border-purple-200"
            >
              {goal.name}
            </span>
          ))}
        </div>
      </div>

      {/* Budget */}
      <div>
        <h3 className="text-md font-semibold text-gray-800 mb-3">Budget</h3>
        <p className="text-gray-700">{data.budget}</p>
      </div>

      {/* Status */}
      <div>
        <h3 className="text-md font-semibold text-gray-800 mb-3">Status</h3>
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
          data.status === 'public' 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-gray-100 text-gray-800 border border-gray-200'
        }`}>
          {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
        </span>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <Button
          onClick={handleSaveForLater}
          className="w-full sm:w-auto px-6 py-3 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
        >
          Save For Later
        </Button>
        <Button
          onClick={handleGenerateProposedSolutions}
          className="w-full sm:w-auto px-6 py-3 border border-primary bg-primary text-white hover:bg-primary/90"
        >
          Generate Proposed Solutions →
        </Button>
      </div>
    </div>
  );
}
