import { useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { ProblemStatementResponse } from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import { Edit2 } from 'lucide-react';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useGetSDG, useGetSDGs } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { useCreateStatementCategoriesOptions } from '@/hooks/useCreateStatementCategoriesOptions';
import { useCreateStatementSubCategoriesOptions } from '@/hooks/useCreateStatementSubCategoriesOptions';
import { Helper } from '@/utils/helpers';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(data.title);
  const [editedDescription, setEditedDescription] = useState(data.description);
  const [editedCountries] = useState(
    data.focusCountries.map(country => country.name),
  );
  const [editedCategories] = useState(
    data.goalsCategories.map(category => category.id),
  );
  const [editedSubcategories] = useState<string[]>([]);

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );

  const { data: categories, isLoading: isLoadingCategories } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const {
    control,
    formState: { errors },
    getValues,
  } = useForm<{
    focusCountries?: string[];
    categories?: string[];
    subcategories?: string[];
  }>({
    defaultValues: {
      focusCountries: editedCountries,
      categories: editedCategories,
      subcategories: editedSubcategories,
    },
  });

  const { data: category, isFetching: isFetchingCategory } = useGetSDG(
    getValues('categories')?.[0] || '',
    {
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
      enabled: !!getValues('categories')?.[0],
    },
  );

  useWatch({
    control,
    defaultValue: {
      categories: [],
    },
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // Here you would typically save the changes to the backend
    console.log('Saving changes:', {
      title: editedTitle,
      description: editedDescription,
    });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Edit Button */}
      <div className="-mt-[72px] flex justify-end">
        {!isEditing ? (
          <Button
            onClick={handleEdit}
            className="flex items-center gap-2 bg-grayNine px-4 py-2 text-white hover:bg-primary"
          >
            Edit
            <Edit2 size={16} />
          </Button>
        ) : (
          <div className="">
            <Button
              onClick={handleSave}
              className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        {!isEditing ? (
          <p className="mb-4 rounded-lg border border-grayFifteen px-4 py-3 text-xl font-semibold text-gray-900">
            {data.title}
          </p>
        ) : (
          <input
            type="text"
            value={editedTitle}
            onChange={e => setEditedTitle(e.target.value)}
            className="mb-4 w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-semibold text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Description */}
      <div>
        {!isEditing ? (
          <div className="prose max-w-none">
            <p className="whitespace-pre-wrap rounded-lg border border-grayFifteen px-4 py-3 text-gray-700">
              {data.description}
            </p>
          </div>
        ) : (
          <textarea
            value={editedDescription}
            onChange={e => setEditedDescription(e.target.value)}
            rows={6}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Focus Countries */}
      <div>
        <FormSelectBox
          name="focusCountries"
          labelName="Focus Countries"
          placeholder="Select Focus Countries"
          control={control}
          errors={errors}
          options={countryOptions}
          optionsArr={countryOptions}
          isMulti
          isLoading={isLoadingCountries}
        />
        {/* )} */}
      </div>

      {/* Focus Industries */}
      <div>
        <FormSelectBox
          options={GoalsHelper.createCategoryOptionsArr(data.industries?.data || [])}
          optionsArr={GoalsHelper.createCategoryOptionsArr(
            industries?.data || [],
          )}
          control={control}
          name="industryCategoryRefs"
          errors={errors}
          labelName="Focus Industries"
          placeholder="Select"
          isClearable={false}
          isMulti
        />
      </div>

      {/* Goals Categories */}
      <div>
        <FormSelectBox
          name="categories"
          labelName="Goals Categories"
          placeholder="Select Goals Categories (Up to 2)"
          control={control}
          errors={errors}
          options={
            useCreateStatementCategoriesOptions(categories?.data || []) || []
          }
          optionsArr={
            useCreateStatementCategoriesOptions(categories?.data || []) || []
          }
          isMulti
          isLoading={isLoadingCategories}
        />
      </div>

      {/* Goals Subcategories */}

      <div className="relative">
        <FormSelectBox
          name="subcategories"
          labelName="Goals SubCategories"
          placeholder="Select Subcategory"
          control={control}
          errors={errors}
          options={
            useCreateStatementSubCategoriesOptions(
              category?.data.subcategories || [],
            ) || []
          }
          optionsArr={
            useCreateStatementSubCategoriesOptions(
              category?.data.subcategories || [],
            ) || []
          }
          isMulti
          isLoading={isFetchingCategory}
        />
        {!getValues('categories')?.[0] && (
          <div className="absolute inset-0 rounded-md bg-gray-100 bg-opacity-50" />
        )}
      </div>
    </div>
  );
}
