import { useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { ProblemStatementResponse } from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import { Edit2 } from 'lucide-react';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import {
  useGetSDG,
  useGetSDGs,
  useGetIndustryCategories,
} from '@/hooks/apiQueryHooks/eduQueryHooks';
import { useCreateStatementCategoriesOptions } from '@/hooks/useCreateStatementCategoriesOptions';
import { useCreateStatementSubCategoriesOptions } from '@/hooks/useCreateStatementSubCategoriesOptions';
import { Helper } from '@/utils/helpers';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(data.title);
  const [editedDescription, setEditedDescription] = useState(data.description);

  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );

  const { data: categories, isLoading: isLoadingCategories } = useGetSDGs({
    staleTime: 1000 * 60 * 60 * 24,
    cacheTime: 1000 * 60 * 60 * 25,
  });

  const { data: industries, isLoading: isLoadingIndustries } =
    useGetIndustryCategories({
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
    });

  // Create options arrays
  const categoryOptions = useCreateStatementCategoriesOptions(
    categories?.data || [],
  );
  const industryOptions = GoalsHelper.createCategoryOptionsArr(
    industries?.data || [],
  );

  // Preselect values from API response data
  const preselectedCountries = countryOptions.filter(option =>
    data.focusCountries.some(country => country.name === option.value),
  );
  const preselectedCategories = categoryOptions.filter(option =>
    data.goalsCategories.some(category => category.id === option.value),
  );
  const preselectedIndustries = industryOptions.filter(option =>
    data.focusIndustries.some(industry => industry.id === option.value),
  );

  const {
    control,
    formState: { errors },
    getValues,
  } = useForm<{
    focusCountries?: any[];
    categories?: any[];
    subcategories?: any[];
    industryCategoryRefs?: any[];
  }>({
    defaultValues: {
      focusCountries: preselectedCountries,
      categories: preselectedCategories,
      subcategories: [],
      industryCategoryRefs: preselectedIndustries,
    },
  });

  // Watch for category changes to fetch subcategories
  const selectedCategories = useWatch({ control, name: 'categories' });
  const firstCategoryRef = selectedCategories?.[0]?.value || '';

  const { data: category, isFetching: isFetchingCategory } = useGetSDG(
    firstCategoryRef,
    {
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
      enabled: !!firstCategoryRef,
    },
  );

  // Create subcategory options from the selected category
  const subcategoryOptions = useCreateStatementSubCategoriesOptions(
    category?.data?.subcategories || [],
  );
  const preselectedSubcategories =
    subcategoryOptions?.filter(option =>
      data.goalsSubcategories?.some(
        subcategory => subcategory.id === option.value,
      ),
    ) || [];

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // Here you would typically save the changes to the backend
    console.log('Saving changes:', {
      title: editedTitle,
      description: editedDescription,
    });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Edit Button */}
      <div className="-mt-[72px] flex justify-end">
        {!isEditing ? (
          <Button
            onClick={handleEdit}
            className="flex items-center gap-2 bg-grayNine px-4 py-2 text-white hover:bg-primary"
          >
            Edit
            <Edit2 size={16} />
          </Button>
        ) : (
          <div className="">
            <Button
              onClick={handleSave}
              className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        {!isEditing ? (
          <p className="mb-4 rounded-lg border border-grayFifteen px-4 py-3 text-xl font-semibold text-gray-900">
            {data.title}
          </p>
        ) : (
          <input
            type="text"
            value={editedTitle}
            onChange={e => setEditedTitle(e.target.value)}
            className="mb-4 w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-semibold text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Description */}
      <div>
        {!isEditing ? (
          <div className="prose max-w-none">
            <p className="whitespace-pre-wrap rounded-lg border border-grayFifteen px-4 py-3 text-gray-700">
              {data.description}
            </p>
          </div>
        ) : (
          <textarea
            value={editedDescription}
            onChange={e => setEditedDescription(e.target.value)}
            rows={6}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Focus Countries */}
      <div>
        <FormSelectBox
          name="focusCountries"
          labelName="Focus Countries"
          placeholder="Select Focus Countries"
          control={control}
          errors={errors}
          options={countryOptions}
          optionsArr={countryOptions}
          isMulti
          isLoading={isLoadingCountries}
        />
      </div>

      {/* Focus Industries */}
      <div>
        <FormSelectBox
          options={industryOptions}
          optionsArr={industryOptions}
          control={control}
          name="industryCategoryRefs"
          errors={errors}
          labelName="Focus Industries"
          placeholder="Select Industries"
          isClearable={false}
          isMulti
          isLoading={isLoadingIndustries}
        />
      </div>

      {/* Goals Categories */}
      <div>
        <FormSelectBox
          name="categories"
          labelName="Goals Categories"
          placeholder="Select Goals Categories"
          control={control}
          errors={errors}
          options={categoryOptions}
          optionsArr={categoryOptions}
          isMulti
          isLoading={isLoadingCategories}
        />
      </div>

      {/* Goals Subcategories */}

      <div className="relative">
        <FormSelectBox
          name="subcategories"
          labelName="Goals SubCategories"
          placeholder="Select Subcategories"
          control={control}
          errors={errors}
          options={subcategoryOptions || []}
          optionsArr={subcategoryOptions || []}
          defaultValue={preselectedSubcategories}
          isMulti
          isLoading={isFetchingCategory}
        />
        {!getValues('categories')?.[0] && (
          <div className="absolute inset-0 rounded-md bg-gray-100 bg-opacity-50" />
        )}
      </div>
    </div>
  );
}
