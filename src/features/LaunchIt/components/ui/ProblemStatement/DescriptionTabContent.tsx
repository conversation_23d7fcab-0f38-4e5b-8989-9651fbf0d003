import { ProblemStatementResponse } from '../../../types';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  return (
    <div className="space-y-6">
      {/* Title */}
      <div>
        <h1 className="mb-4 text-2xl font-bold text-gray-900">{data.title}</h1>
      </div>

      {/* Description */}
      <div>
        <h2 className="mb-3 text-lg font-semibold text-gray-800">
          Description
        </h2>
        <div className="prose max-w-none">
          <p className="whitespace-pre-wrap leading-relaxed text-gray-700">
            {data.description}
          </p>
        </div>
      </div>

      {/* Focus Countries */}
      <div className='flex gap-2 items-center'>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Focus Countries
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.focusCountries.map(country => (
            <span
              key={country.code}
              className="inline-flex items-center rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-sm text-blue-800"
            >
              🇳🇬 {country.name}
            </span>
          ))}
        </div>
      </div>

      {/* Focus Industries */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Focus Industries
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.focusIndustries.map(industry => (
            <span
              key={industry.id}
              className="bg-green-100 text-green-800 border-green-200 inline-flex items-center rounded-full border px-3 py-1 text-sm"
            >
              {industry.name}
            </span>
          ))}
        </div>
      </div>

      {/* Goals Categories */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">
          Goals Categories
        </h3>
        <div className="flex flex-wrap gap-2">
          {data.goalsCategories.map(goal => (
            <span
              key={goal.id}
              className="bg-purple-100 text-purple-800 border-purple-200 inline-flex items-center rounded-full border px-3 py-1 text-sm"
            >
              {goal.name}
            </span>
          ))}
        </div>
      </div>

      {/* Budget */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">Budget</h3>
        <p className="text-gray-700">{data.budget}</p>
      </div>

      {/* Status */}
      <div>
        <h3 className="text-md mb-3 font-semibold text-gray-800">Status</h3>
        <span
          className={`inline-flex items-center rounded-full px-3 py-1 text-sm ${
            data.status === 'public'
              ? 'bg-green-100 text-green-800 border-green-200 border'
              : 'border border-gray-200 bg-gray-100 text-gray-800'
          }`}
        >
          {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
        </span>
      </div>
    </div>
  );
}
