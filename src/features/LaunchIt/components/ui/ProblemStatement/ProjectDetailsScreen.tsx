import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Edit } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProposedProject, ProposedSolution } from '../../../services/launchItApiRequests';
import { useUserContext } from '@/context/user/UserContext';
import { useCustomToast } from '@/hooks/useToast';
import { useAppContext } from '@/context/event/AppEventContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';

export default function ProjectDetailsScreen() {
  const { data } = useUserContext();
  const location = useLocation();
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { successToast } = useCustomToast();

  // Get the proposed projects, current project, and selected solution from location state
  const { proposedProjects, selectedSolution } = location.state || {};
  const currentProject = proposedProjects?.find(
    (project: ProposedProject) => project.referenceId === projectRef,
  );

  const handleBack = () => {
    navigate(-1);
  };

  const handleSaveForLater = () => {
    successToast('Project saved for later.');
    navigate(`/${currentAccountType}/dashboard-statements`);
  };

  const handleGenerateProposedProjects = () => {
    // Placeholder for future implementation
    successToast('Generate Proposed Projects functionality coming soon!');
  };

  if (!currentProject) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">Project not found.</p>
        <Button onClick={handleBack} className="mt-4">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white px-6 py-4 shadow-sm">
        <div className="flex items-center gap-2">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Project Details</span>
          </button>
        </div>

        {/* First Action Buttons */}
        <div className="mt-6 flex flex-col gap-4 sm:flex-row">
          <Button
            onClick={handleSaveForLater}
            className="w-full border border-gray-300 bg-white px-6 py-3 text-gray-700 hover:bg-gray-50 sm:w-auto"
          >
            Save For Later
          </Button>
          <Button
            onClick={handleGenerateProposedProjects}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 sm:w-auto"
          >
            Generate Proposed Projects →
          </Button>
        </div>
      </div>

      <div className="mx-auto max-w-4xl space-y-6 p-6">
        {/* Project Overview */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-start gap-4">
            <img
              src={solutionThumbnail}
              alt={currentProject.name}
              className="h-24 w-24 rounded-lg object-cover"
            />
            <div className="flex-1">
              <div className="mb-2 flex items-center gap-2">
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentProject.name}
                </h1>
                <button className="rounded-full p-1 hover:bg-gray-100">
                  <Edit size={16} className="text-gray-500" />
                </button>
              </div>
              <p className="text-gray-600">{currentProject.description}</p>
            </div>
          </div>

          {/* Project Details Grid */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h3 className="mb-3 font-semibold text-gray-900">Focus Countries</h3>
              <div className="flex flex-wrap gap-2">
                {currentProject.focusCountries?.map((country, index) => (
                  <span
                    key={index}
                    className="rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-800"
                  >
                    {country}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <h3 className="mb-3 font-semibold text-gray-900">Project Level</h3>
              <span className="rounded-full bg-green-100 px-3 py-1 text-sm text-green-800">
                {currentProject.minimumProjectLevel?.replace(/_/g, ' ') || 'Not specified'}
              </span>
            </div>

            <div>
              <h3 className="mb-3 font-semibold text-gray-900">Estimated Completion</h3>
              <p className="text-gray-700">
                {currentProject.estimatedCompletionTimeInMonths} months
              </p>
            </div>

            <div>
              <h3 className="mb-3 font-semibold text-gray-900">Team Size</h3>
              <p className="text-gray-700">
                {currentProject.roleAndHeadCounts?.reduce((total, role) => total + role.headCount, 0) || 0} members
              </p>
            </div>
          </div>
        </div>

        {/* Solution Statement */}
        {selectedSolution?.solutionStatement && (
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              Related Solution Statement
            </h2>
            <p className="text-gray-700">{selectedSolution.solutionStatement}</p>
          </div>
        )}

        {/* Team Roles */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Required Team Roles</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {currentProject.roleAndHeadCounts?.map((role, index) => (
              <div key={index} className="flex items-center justify-between rounded-lg border border-gray-200 p-4">
                <span className="font-medium text-gray-900">{role.role}</span>
                <span className="rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                  {role.headCount} {role.headCount === 1 ? 'person' : 'people'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Goals */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Project Goals</h2>
          <div className="space-y-4">
            {currentProject.goals?.map((goal, index) => (
              <div key={index} className="border-l-4 border-primary pl-4">
                <h4 className="font-semibold text-gray-900">{goal.description}</h4>
                <div className="mt-2 space-y-2">
                  {goal.goalsKeyResults?.map((keyResult, krIndex) => (
                    <div key={krIndex} className="border-l-4 border-gray-200 pl-4">
                      <p className="text-gray-700">{keyResult.keyResultDescription}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Competitors and Collaborators */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">Known Competitors</h2>
            <div className="space-y-2">
              {currentProject.knownCompetitors?.map((competitor, index) => (
                <p key={index} className="text-gray-700">{competitor}</p>
              ))}
            </div>
          </div>

          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">Potential Collaborators</h2>
            <div className="space-y-2">
              {currentProject.potentialCollaborators?.map((collaborator, index) => (
                <p key={index} className="text-gray-700">{collaborator}</p>
              ))}
            </div>
          </div>
        </div>

        {/* Second Action Buttons */}
        <div className="flex flex-col gap-4 sm:flex-row">
          <Button
            onClick={handleSaveForLater}
            className="w-full border border-gray-300 bg-white px-6 py-3 text-gray-700 hover:bg-gray-50 sm:w-auto"
          >
            Save For Later
          </Button>
          <Button
            onClick={handleGenerateProposedProjects}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 sm:w-auto"
          >
            Generate Proposed Projects →
          </Button>
        </div>
      </div>
    </div>
  );
}
