import { useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useUserContext } from '@/context/user/UserContext';

import {
  CreateProblemStatementFormData,
  ProblemStatementResponse,
} from '../../../types';
import { CreateProblemStatementProvider } from '../../../context/CreateProblemStatementContext';
import ProblemStatementFormScreen from './ProblemStatementFormScreen';
import ProblemStatementResultScreen from './ProblemStatementResultScreen';

export default function CreateProblemStatement() {
  const { data } = useUserContext();
  const navigate = useNavigate();
  const [currentScreen, setCurrentScreen] = useState<'form' | 'result'>('form');
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementResponse | null>(null);

  const handleFormSubmit = (formData: CreateProblemStatementFormData) => {
    // Mock API response - in real implementation, this would be an API call
    const mockResponse: ProblemStatementResponse = {
      id: 'ps-' + Date.now(),
      title: 'Build A Healthcare Management System',
      description: formData.description,
      focusCountries: [
        { code: 'NG', name: 'Nigeria' },
        { code: 'FR', name: 'France' },
        { code: 'GH', name: 'Ghana' },
      ],
      focusIndustries: [
        { id: 'healthcare', name: 'Healthcare' },
        { id: 'quality-education', name: 'Quality Education' },
      ],
      goalsCategories: [
        { id: 'sdg-3', name: 'Good Health and Well-being' },
        { id: 'sdg-4', name: 'Quality Education' },
      ],
      budget: formData.budget,
      status: formData.status,
      videoLink: formData.videoLink,
      attachedFiles: formData.detailedDescriptionFile
        ? [
            {
              id: 'file-1',
              name: formData.detailedDescriptionFile.name,
              url: URL.createObjectURL(formData.detailedDescriptionFile),
              type: formData.detailedDescriptionFile.type,
              size: formData.detailedDescriptionFile.size,
            },
          ]
        : [],
      createdBy: {
        firstName: data?.user?.firstName || 'Tunde',
        lastName: data?.user?.lastName || 'Emmanuel',
        avatar: data?.user?.profilePicture || undefined,
      },
      createdAt: new Date().toISOString(),
    };

    setProblemStatementData(mockResponse);
    setCurrentScreen('result');
  };

  const handleBackToForm = () => {
    setCurrentScreen('form');
  };

  return (
    <CreateProblemStatementProvider>
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <div
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
            onClick={() =>
              currentScreen === 'form' ? navigate(-1) : handleBackToForm()
            }
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <p className="text-lg font-medium">Create a problem statement</p>
          </div>
        </div>

        {currentScreen === 'form' ? (
          <ProblemStatementFormScreen
            onSubmit={handleFormSubmit}
            userName={data?.user?.firstName}
          />
        ) : (
          <ProblemStatementResultScreen
            data={problemStatementData}
            onBack={handleBackToForm}
          />
        )}
      </div>
    </CreateProblemStatementProvider>
  );
}
