import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useUserContext } from '@/context/user/UserContext';
import Form from '@/components/forms/Form';
import FormTextAreaBox from '@/components/forms/FormTextAreaBox';

export default function CreateProblemStatement() {
  const { data } = useUserContext();
  const navigate = useNavigate();

  return (
    <div>
      <div className="flex items-center gap-2 px-3.5">
        <div
          className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft size={24} className="-mt-0.5 text-primary" />
          <p className="text-lg font-medium">Create a problem</p>
        </div>
      </div>

      <div className="flex flex-col items-center px-12">
        <div className="my-8 w-full max-w-screen-md rounded-md border border-dashed border-grayNine px-12 py-4 text-center">
          <p className="">
            {`${data?.user?.firstName}`}, now let’s understand the challenge.
          </p>
        </div>

        <div>
          <Form
            // onSubmit={handleSubmit(onSubmit)}
            className="w-full max-w-[776px]"
          >
            {/* Form elements */}
          </Form>
        </div>
      </div>
    </div>
  );
}
