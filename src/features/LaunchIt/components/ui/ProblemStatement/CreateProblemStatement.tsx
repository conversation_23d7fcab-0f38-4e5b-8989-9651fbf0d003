import { useState, useEffect } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { useUserContext } from '@/context/user/UserContext';
import { useAppContext } from '@/context/event/AppEventContext';

import {
  ProblemStatementResponse,
  ProblemStatementApiResponse,
} from '../../../types';
import { CreateProblemStatementProvider } from '../../../context/CreateProblemStatementContext';
import ProblemStatementFormScreen from './ProblemStatementFormScreen';
import ProblemStatementResultScreen from './ProblemStatementResultScreen';
import { useGetProblemStatement } from '../../../hooks/apiQueryHooks/launchItQueryHooks';

export default function CreateProblemStatement() {
  const { data } = useUserContext();
  const { currentAccountType } = useAppContext();
  const navigate = useNavigate();
  const { problemStatementRef } = useParams();

  const [currentScreen, setCurrentScreen] = useState<'form' | 'result'>(
    problemStatementRef ? 'result' : 'form',
  );
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementResponse | null>(null);

  // API fallback when problemStatementRef is present in URL
  const shouldFetchProblemStatement =
    !!problemStatementRef &&
    problemStatementRef.trim() !== '' &&
    !problemStatementData &&
    currentScreen === 'result';

  const {
    data: problemStatementFromApi,
    isLoading: isLoadingProblemStatement,
    error: problemStatementError,
  } = useGetProblemStatement(problemStatementRef || '', {
    enabled: shouldFetchProblemStatement,
  });

  // Transform API data when fetched
  useEffect(() => {
    if (problemStatementFromApi && !problemStatementData) {
      // The API service returns the data directly (res?.data?.data)
      const apiData = problemStatementFromApi;
      const transformedResponse: ProblemStatementResponse = {
        id: apiData.problemStatementRef,
        problemStatementRef: apiData.problemStatementRef,
        title: apiData.title,
        description: apiData.refinedDescription || apiData.description,
        focusCountries: (apiData.countries || []).map((country: string) => ({
          code: country.substring(0, 2).toUpperCase(),
          name: country,
        })),
        focusIndustries: [],
        goalsCategories: (apiData.categoryRefList || []).map(
          (categoryRef: string) => ({
            id: categoryRef,
            name: categoryRef,
          }),
        ),
        goalsSubcategories: (apiData.subcategoryRefList || []).map(
          (subcategoryRef: string) => ({
            id: subcategoryRef,
            name: subcategoryRef,
          }),
        ),
        budget: `$${(apiData.budget || 0).toLocaleString()}`,
        status: (apiData.visibility?.toLowerCase() || 'private') as
          | 'public'
          | 'private',
        videoLink: apiData.descVidUrl || '',
        attachedFiles: apiData.descDocUrl
          ? [
              {
                id: 'file-1',
                name: 'Description Document',
                url: apiData.descDocUrl,
                type: 'application/pdf',
                size: 0,
              },
            ]
          : [],
        createdBy: {
          firstName:
            apiData.createdByName?.split(' ')[0] ||
            data?.user?.firstName ||
            'User',
          lastName:
            apiData.createdByName?.split(' ')[1] || data?.user?.lastName || '',
          avatar: data?.user?.profilePicture || undefined,
        },
        createdAt: apiData.createdAt,
      };
      setProblemStatementData(transformedResponse);
    }
  }, [problemStatementFromApi, problemStatementData, data?.user]);

  const handleFormSubmit = (apiResponseData: ProblemStatementApiResponse) => {
    // Transform API response to match the expected format
    const transformedResponse: ProblemStatementResponse = {
      id: apiResponseData.problemStatementRef,
      problemStatementRef: apiResponseData.problemStatementRef,
      title: apiResponseData.title,
      description:
        apiResponseData.refinedDescription || apiResponseData.description,
      focusCountries: (apiResponseData.countries || []).map(
        (country: string) => ({
          code: country.substring(0, 2).toUpperCase(), // Simple code generation
          name: country,
        }),
      ),
      focusIndustries: [], // Industries will be selected in edit mode
      goalsCategories: (apiResponseData.categoryRefList || []).map(
        (categoryRef: string) => ({
          id: categoryRef,
          name: categoryRef, // Will be resolved to actual names in DescriptionTabContent
        }),
      ),
      goalsSubcategories: (apiResponseData.subcategoryRefList || []).map(
        (subcategoryRef: string) => ({
          id: subcategoryRef,
          name: subcategoryRef, // Will be resolved to actual names in DescriptionTabContent
        }),
      ),
      budget: `$${(apiResponseData.budget || 0).toLocaleString()}`,
      status: (apiResponseData.visibility?.toLowerCase() || 'private') as
        | 'public'
        | 'private',
      videoLink: apiResponseData.descVidUrl || '',
      attachedFiles: apiResponseData.descDocUrl
        ? [
            {
              id: 'file-1',
              name: 'Description Document',
              url: apiResponseData.descDocUrl,
              type: 'application/pdf',
              size: 0, // Size not provided in API response
            },
          ]
        : [],
      createdBy: {
        firstName:
          apiResponseData.createdByName?.split(' ')[0] ||
          data?.user?.firstName ||
          'User',
        lastName:
          apiResponseData.createdByName?.split(' ')[1] ||
          data?.user?.lastName ||
          '',
        avatar: data?.user?.profilePicture || undefined,
      },
      createdAt: apiResponseData.createdAt,
    };

    setProblemStatementData(transformedResponse);
    setCurrentScreen('result');

    // Update URL to include problemStatementRef for proper navigation and refresh handling
    if (currentAccountType) {
      navigate(
        `/${currentAccountType}/launch/problem-statement/create/${apiResponseData.problemStatementRef}`,
        { replace: true },
      );
    }
  };

  const handleBackToForm = () => {
    setCurrentScreen('form');
  };

  // Show loading state when fetching problem statement from API
  // Only show loading if we should be fetching and are actually loading
  if (shouldFetchProblemStatement && isLoadingProblemStatement) {
    return (
      <CreateProblemStatementProvider>
        <div>
          <div className="flex items-center gap-2 px-3.5">
            <button
              className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
              onClick={() => navigate(-1)}
            >
              <ArrowLeft size={24} className="-mt-0.5 text-primary" />
              <span className="text-lg font-medium">
                Create a problem statement
              </span>
            </button>
          </div>

          <div className="-mt-48 flex h-screen items-center justify-center">
            <div className="text-center">
              <div className="mb-4">
                <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                Loading problem statement...
              </h2>
              <p className="mt-2 text-gray-600">
                Please wait while we fetch the problem statement details.
              </p>
            </div>
          </div>
        </div>
      </CreateProblemStatementProvider>
    );
  }

  // Show error state when problem statement is not found
  if (problemStatementRef && !problemStatementData && problemStatementError) {
    return (
      <CreateProblemStatementProvider>
        <div>
          <div className="flex items-center gap-2 px-3.5">
            <button
              className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
              onClick={() => navigate(-1)}
            >
              <ArrowLeft size={24} className="-mt-0.5 text-primary" />
              <span className="text-lg font-medium">
                Create a problem statement
              </span>
            </button>
          </div>

          <div className="-mt-48 flex h-screen items-center justify-center">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900">
                Problem statement not available
              </h2>
              <p className="mt-2 text-gray-600">
                Failed to load problem statement details. Please try again.
              </p>
            </div>
          </div>
        </div>
      </CreateProblemStatementProvider>
    );
  }

  return (
    <CreateProblemStatementProvider>
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
            onClick={() =>
              currentScreen === 'form' ? navigate(-1) : handleBackToForm()
            }
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">
              Create a problem statement
            </span>
          </button>
        </div>

        {currentScreen === 'form' ? (
          <ProblemStatementFormScreen
            onSubmit={handleFormSubmit}
            userName={data?.user?.firstName}
          />
        ) : (
          <ProblemStatementResultScreen
            data={problemStatementData}
            onBack={handleBackToForm}
          />
        )}
      </div>
    </CreateProblemStatementProvider>
  );
}
