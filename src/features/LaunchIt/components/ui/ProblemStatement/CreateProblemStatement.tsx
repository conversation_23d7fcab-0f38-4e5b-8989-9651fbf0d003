import { useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useUserContext } from '@/context/user/UserContext';

import { ProblemStatementResponse } from '../../../types';
import { CreateProblemStatementProvider } from '../../../context/CreateProblemStatementContext';
import ProblemStatementFormScreen from './ProblemStatementFormScreen';
import ProblemStatementResultScreen from './ProblemStatementResultScreen';

export default function CreateProblemStatement() {
  const { data } = useUserContext();
  const navigate = useNavigate();
  const [currentScreen, setCurrentScreen] = useState<'form' | 'result'>('form');
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementResponse | null>(null);

  const handleFormSubmit = (apiResponseData: any) => {
    // Transform API response to match the expected format
    const transformedResponse: ProblemStatementResponse = {
      id: apiResponseData.problemStatementRef,
      problemStatementRef: apiResponseData.problemStatementRef,
      title: apiResponseData.title,
      description:
        apiResponseData.refinedDescription || apiResponseData.description,
      focusCountries: (apiResponseData.countries || []).map(
        (country: string) => ({
          code: country.substring(0, 2).toUpperCase(), // Simple code generation
          name: country,
        }),
      ),
      focusIndustries: [], // Categories not provided in API response
      goalsCategories: [], // Categories not provided in API response
      budget: `$${(apiResponseData.budget || 0).toLocaleString()}`,
      status: (apiResponseData.visibility?.toLowerCase() || 'private') as
        | 'public'
        | 'private',
      videoLink: apiResponseData.descVidUrl || undefined,
      attachedFiles: apiResponseData.descDocUrl
        ? [
            {
              id: 'file-1',
              name: 'Description Document',
              url: apiResponseData.descDocUrl,
              type: 'application/pdf',
              size: 0, // Size not provided in API response
            },
          ]
        : [],
      createdBy: {
        firstName:
          apiResponseData.createdByName?.split(' ')[0] ||
          data?.user?.firstName ||
          'User',
        lastName:
          apiResponseData.createdByName?.split(' ')[1] ||
          data?.user?.lastName ||
          '',
        avatar: data?.user?.profilePicture || undefined,
      },
      createdAt: apiResponseData.createdAt,
    };

    setProblemStatementData(transformedResponse);
    setCurrentScreen('result');
  };

  const handleBackToForm = () => {
    setCurrentScreen('form');
  };

  return (
    <CreateProblemStatementProvider>
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
            onClick={() =>
              currentScreen === 'form' ? navigate(-1) : handleBackToForm()
            }
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">
              Create a problem statement
            </span>
          </button>
        </div>

        {currentScreen === 'form' ? (
          <ProblemStatementFormScreen
            onSubmit={handleFormSubmit}
            userName={data?.user?.firstName}
          />
        ) : (
          <ProblemStatementResultScreen
            data={problemStatementData}
            onBack={handleBackToForm}
          />
        )}
      </div>
    </CreateProblemStatementProvider>
  );
}
