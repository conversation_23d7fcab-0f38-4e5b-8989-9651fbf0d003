import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

interface ProblemStatementActionButtonsProps {
  onSaveForLater: () => void;
  onGenerateProposedSolutions: () => void;
  isGeneratingProposedSolutions?: boolean;
}

export default function ProblemStatementActionButtons({
  onSaveForLater,
  onGenerateProposedSolutions,
  isGeneratingProposedSolutions = false,
}: ProblemStatementActionButtonsProps) {
  return (
    <div className="flex flex-col gap-4 pt-6 sm:flex-row">
      <Button
        onClick={onSaveForLater}
        className="w-full border border-black px-6 py-3 text-darkGray hover:border-primary hover:text-primary sm:w-auto"
      >
        Save For Later
      </Button>
      <Button
        onClick={onGenerateProposedSolutions}
        disabled={isGeneratingProposedSolutions}
        className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:bg-darkGray sm:w-auto"
      >
        {isGeneratingProposedSolutions ? (
          <div className="flex items-center gap-2">
            <Spinner className="h-4 w-4" />
            <span>Hang tight, almost there...</span>
          </div>
        ) : (
          'Generate Proposed Solutions →'
        )}
      </Button>
    </div>
  );
}
