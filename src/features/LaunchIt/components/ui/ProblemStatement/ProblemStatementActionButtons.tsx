import Button from '@/components/ui/ButtonComponent';
import Spinner from '@/components/ui/Spinner';

interface ProblemStatementActionButtonsProps {
  onSaveForLater: () => void;
  onGenerateProposedSolutions: () => void;
  isGeneratingProposedSolutions?: boolean;
}

export default function ProblemStatementActionButtons({
  onSaveForLater,
  onGenerateProposedSolutions,
  isGeneratingProposedSolutions = false,
}: ProblemStatementActionButtonsProps) {
  return (
    <div className="flex flex-col gap-4 pt-6 sm:flex-row">
      <Button
        onClick={onSaveForLater}
        className="text-darkGray w-full border border-black bg-white px-6 py-3 hover:border-primary hover:text-primary sm:w-auto"
      >
        Save For Later
      </Button>
      <Button
        onClick={onGenerateProposedSolutions}
        disabled={isGeneratingProposedSolutions}
        className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50 sm:w-auto"
      >
        {isGeneratingProposedSolutions ? (
          <div className="flex items-center gap-2">
            <Spinner size="sm" />
            <span>Hang tight, almost there...</span>
          </div>
        ) : (
          'Generate Proposed Solutions →'
        )}
      </Button>
    </div>
  );
}
