import Button from '@/components/ui/ButtonComponent';

interface ProblemStatementActionButtonsProps {
  onSaveForLater: () => void;
  onGenerateProposedSolutions: () => void;
}

export default function ProblemStatementActionButtons({
  onSaveForLater,
  onGenerateProposedSolutions,
}: ProblemStatementActionButtonsProps) {
  return (
    <div className="flex flex-col gap-4 pt-6 sm:flex-row">
      <Button
        onClick={onSaveForLater}
        className="text-darkGray w-full border border-black bg-white px-6 py-3 hover:border-primary hover:text-primary sm:w-auto"
      >
        Save For Later
      </Button>
      <Button
        onClick={onGenerateProposedSolutions}
        className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 sm:w-auto"
      >
        Generate Proposed Solutions →
      </Button>
    </div>
  );
}
