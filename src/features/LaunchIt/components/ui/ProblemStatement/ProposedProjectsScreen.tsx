import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProposedProject, ProposedSolution } from '../../../services/launchItApiRequests';
import { useUserContext } from '@/context/user/UserContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';

export default function ProposedProjectsScreen() {
  const { data } = useUserContext();
  const location = useLocation();
  const navigate = useNavigate();

  const { proposedProjects, selectedSolution } =
    (location.state as {
      proposedProjects: ProposedProject[];
      selectedSolution: ProposedSolution;
    }) || {};

  const handleBack = () => {
    navigate(-1);
  };

  const handleViewProject = (projectRef: string) => {
    // Navigate to individual project view
    navigate(
      `/${data?.user?.accountType?.toLowerCase()}/launch/proposed-projects/${projectRef}`,
      {
        state: { 
          proposedProjects, 
          projectRef,
          selectedSolution 
        },
      },
    );
  };

  if (!proposedProjects || proposedProjects.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">No proposed projects available.</p>
        <Button onClick={handleBack} className="mt-4">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="">
      <div className="flex items-center gap-2 px-3.5">
        <button
          onClick={handleBack}
          className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
        >
          <ArrowLeft size={24} className="-mt-0.5 text-primary" />
          <span className="text-lg font-medium">
            Proposed Solution #{selectedSolution?.proposedSolutionRef?.slice(-4) || '1'}
          </span>
        </button>
      </div>

      <div className="flex flex-col items-center px-12">
        <div className="my-8 w-full max-w-screen-md rounded-md border border-dashed border-grayNine bg-white p-4">
          <p className="text-darkGray text-center">
            {data?.user?.firstName
              ? `${data?.user?.firstName}, now let's select one of ${proposedProjects.length} proposed projects below.`
              : `Now let's select one of ${proposedProjects.length} proposed projects below.`}
          </p>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 gap-6 px-4 md:grid-cols-2 lg:grid-cols-3">
        {proposedProjects.map((project) => (
          <div
            key={project.referenceId}
            className="relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
          >
            {/* Admitted Label */}
            <div className="absolute right-0 top-0 z-10">
              <span className="bg-greenOne px-2 py-1 text-xs font-medium text-white">
                Admitted
              </span>
            </div>

            {/* Image */}
            <div className="aspect-video bg-gray-100">
              <img
                src={solutionThumbnail}
                alt={project.name}
                className="h-full w-full object-cover"
              />
            </div>

            {/* Content */}
            <div className="p-4">
              <h3 className="mb-2 text-sm font-bold text-gray-900">
                EduTech
              </h3>

              <div className="mb-3 space-y-1 text-[10px] font-semibold text-gray-600">
                <p>
                  <span className="font-medium">Goals Categories:</span>{' '}
                  Good Health & Well-Being, Quality Education
                </p>
                <p>
                  <span className="font-medium">Institution:</span>{' '}
                  University of Port Harcourt
                </p>
                <p>
                  <span className="font-medium">Country Focus:</span>{' '}
                  {project.focusCountries?.join(', ') || 'Nigeria, Ghana, Albania'}
                </p>
              </div>

              <Button
                onClick={() => handleViewProject(project.referenceId)}
                className="w-full bg-primary text-sm text-white hover:bg-primary/80"
              >
                View Project
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
