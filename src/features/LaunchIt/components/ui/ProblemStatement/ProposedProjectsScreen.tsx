import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import {
  ProposedProject,
  ProposedSolution,
} from '../../../services/launchItApiRequests';
import { useUserContext } from '@/context/user/UserContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';
import { sdgGoalsCategories } from '@/features/LaunchIt/data/constants';
// import SearchBar from '@/components/ui/SearchBar';

export default function ProposedProjectsScreen() {
  const { data } = useUserContext();
  const location = useLocation();
  const navigate = useNavigate();

  const { proposedProjects, selectedSolution } =
    (location.state as {
      proposedProjects: ProposedProject[];
      selectedSolution: ProposedSolution;
    }) || {};

  const handleBack = () => {
    navigate(-1);
  };

  const handleViewProject = (projectRef: string) => {
    // Navigate to individual project view
    navigate(
      `/${data?.user?.accountType?.toLowerCase()}/launch/proposed-projects/${projectRef}`,
      {
        state: {
          proposedProjects,
          projectRef,
          selectedSolution,
        },
      },
    );
  };

  if (!proposedProjects || proposedProjects.length === 0) {
    return (
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Go Back</span>
          </button>
        </div>
        <div className="-mt-48 flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Projects not available
            </h2>
            <p className="mt-2 text-gray-600">
              The requested projects could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <div className="flex items-center justify-between px-3.5">
        <button
          onClick={handleBack}
          className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
        >
          <ArrowLeft size={24} className="-mt-0.5 text-primary" />
          <span className="text-lg font-medium">Proposed Projects</span>
        </button>

        {/* <div>
          <SearchBar
            // setSearchValue={value => handleChange('country', value)}
            // defaultValue={searchValue?.country || ''}
            containerCls="z-[1] sticky top-0 rounded-lg bg-white"
            className="bg-white"
          />
        </div> */}
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 gap-6 px-4 py-8 md:grid-cols-2 lg:grid-cols-3">
        {proposedProjects.map(project => (
          <div
            key={project.referenceId}
            className="relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
          >
            {/* Admitted Label */}
            <div className="absolute right-0 top-0 z-10">
              <span className="bg-greenOne px-2 py-1 text-xs font-medium text-white">
                Admitted
              </span>
            </div>

            {/* Image */}
            <div className="aspect-video bg-gray-100">
              <img
                src={solutionThumbnail}
                alt={project.name}
                className="h-full w-full object-cover"
              />
            </div>

            {/* Content */}
            <div className="p-4">
              <h3 className="mb-2 text-sm font-bold text-gray-900">
                {project.name}
              </h3>

              <div className="text-darkGray mb-3 space-y-2 text-[10px] font-semibold">
                <p className="rounded bg-grayNineTeen p-2">
                  <span className="font-medium">Goals Categories:</span>{' '}
                  {sdgGoalsCategories
                    .filter(cat =>
                      project.focusGoalRefs.includes(cat.categoryRef),
                    )
                    .map(cat => cat.categoryName)
                    .join(', ') ||
                    'Good Health & Well-Being, Quality Education'}
                </p>
                <p className="rounded bg-grayNineTeen p-2">
                  <span className="font-medium">Institution:</span>{' '}
                  {/* {project.institution}*/}University of Lagos
                </p>
                <p className="rounded bg-grayNineTeen p-2">
                  <span className="font-medium">Country Focus:</span>{' '}
                  {project.focusCountries?.join(', ') || 'Nigeria'}
                </p>
              </div>

              <Button
                onClick={() => handleViewProject(project.referenceId)}
                className="w-full bg-primary text-sm text-white hover:bg-primary/80"
              >
                View Project
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
