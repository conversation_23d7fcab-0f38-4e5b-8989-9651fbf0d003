import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, PencilLine, Plus } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProposedProject } from '../../../services/launchItApiRequests';
import { useCustomToast } from '@/hooks/useToast';
import { useAppContext } from '@/context/event/AppEventContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';
import {
  ReactElement,
  JSXElementConstructor,
  ReactNode,
  ReactPortal,
  Key,
} from 'react';

export default function ProposedProjectDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const { projectRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { successToast } = useCustomToast();

  // Get the proposed projects, current project, and selected solution from location state
  const { proposedProjects, selectedSolution } = location.state || {};
  const currentProject = proposedProjects?.find(
    (project: ProposedProject) => project.referenceId === projectRef,
  );

  const handleBack = () => {
    navigate(-1);
  };

  const handleGenerateProposedProjects = () => {
    successToast(
      'Generating Project Automation Summary in background. This might take a few minutes.',
    );
    setTimeout(() => {
      navigate(`/${currentAccountType}/dashboard-statements`);
    }, 2500);
  };

  if (!currentProject) {
    return (
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Go Back</span>
          </button>
        </div>

        <div className="-mt-48 flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Project not available
            </h2>
            <p className="mt-2 text-gray-600">
              The requested project could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Project Details</span>
          </button>
        </div>

        {/* First Action Buttons */}
        <div className="mt-6 flex flex-col gap-4 sm:flex-row">
          <Button
            onClick={handleGenerateProposedProjects}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 sm:w-auto"
          >
            Generate Automation Summary →
          </Button>
        </div>
      </div>

      <div className="mx-auto max-w-4xl space-y-6 p-6">
        <div className="flex w-full items-center justify-center">
          <img
            src={solutionThumbnail}
            alt="Project Thumbnail"
            className="h-64 w-80 rounded-2xl object-cover"
          />
        </div>

        {/* Proposed Goal Section */}
        <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="mb-4 flex items-center justify-between rounded-t-lg bg-darkGray p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Goal
            </div>
            <button className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200">
              Edit
              <PencilLine size={20} />
            </button>
          </div>
          <div className="px-2 text-xl font-bold capitalize text-gray-900">
            {currentProject.goals[0]?.description || ''}
          </div>
          {selectedSolution?.solutionStatement && (
            <p className="p-2 leading-relaxed text-gray-700">
              {selectedSolution.solutionStatement}
            </p>
          )}

          <div className="px-2">
            {currentProject.goals[0]?.goalsKeyResults.map(
              (
                keyResult: {
                  referenceId: Key | null | undefined;
                  keyResultDescription:
                    | string
                    | number
                    | boolean
                    | ReactElement<any, string | JSXElementConstructor<any>>
                    | Iterable<ReactNode>
                    | ReactPortal
                    | null
                    | undefined;
                },
                index: number,
              ) => (
                <div
                  key={keyResult.referenceId}
                  className="mb-2 rounded-xl border border-gray-200 bg-peachOne p-2"
                >
                  <div className="mb-4 w-fit rounded border border-gray-200 bg-white px-4 py-1.5 font-medium">
                    Key Result {index + 1}
                  </div>
                  <p className="leading-relaxed text-gray-700">
                    {keyResult.keyResultDescription}
                  </p>
                </div>
              ),
            )}

            <div className="mb-2 w-fit rounded-xl border border-gray-200 bg-peachOne p-2">
              <button className="flex items-center gap-2 rounded border border-gray-200 bg-white px-4 py-1.5 font-medium hover:bg-grayNineTeen">
                Add
                <Plus size={20} className="-mt-1" />
              </button>
            </div>
          </div>
        </div>

        {/* Proposed Project */}
        <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="mb-4 flex items-center justify-between rounded-t-lg bg-darkGray p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Project
            </div>
            <button className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200">
              Edit
              <PencilLine size={20} />
            </button>
          </div>
          <div className="px-2 text-xl font-bold capitalize text-gray-900">
            {currentProject.name || ''}
          </div>
          <p className="p-2 leading-relaxed text-gray-700">
            {currentProject.description || ''}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end">
          <Button
            onClick={handleGenerateProposedProjects}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:bg-darkGray sm:w-auto"
          >
            Generate Automation Summary →
          </Button>
        </div>
      </div>
    </div>
  );
}
