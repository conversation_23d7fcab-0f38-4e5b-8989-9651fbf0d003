import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { problemCards } from '../../../data/constant';
import CardOption from '../../../components/ui/CardOption';
import { useUserContext } from '@/context/user/UserContext';

export default function ProblemStatementOptions() {
  const { data } = useUserContext();
  const navigate = useNavigate();

  return (
    <div>
      <div className="flex items-center gap-2 px-3.5">
        <div
          className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft size={24} className="-mt-0.5 text-primary" />
          <p className="text-lg font-medium">Start with a problem</p>
        </div>
      </div>
      <div className="flex flex-col items-center px-12">
        <div className="my-8 w-full max-w-screen-md rounded-md border border-dashed border-grayNine px-12 py-2.5 text-center">
          <p className="text-xl">Hi {`${data?.user?.firstName}`}!</p>
          <p className="mt-2">Create a problem statement or choose one.</p>
        </div>

        <div className="flex justify-center">
          <div className="flex flex-wrap items-center justify-center gap-6">
            {problemCards.map((card, index) => (
              <CardOption
                key={index}
                {...card}
                onClick={() =>
                  card.route
                    ? navigate(card.route)
                    : console.warn('No route defined for', card.title)
                }
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
