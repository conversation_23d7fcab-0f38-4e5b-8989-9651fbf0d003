import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Edit } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProposedSolution } from '../../../services/launchItApiRequests';
import { useUserContext } from '@/context/user/UserContext';
import { useCustomToast } from '@/hooks/useToast';
import { useAppContext } from '@/context/event/AppEventContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';
import {
  Key,
  ReactElement,
  JSXElementConstructor,
  ReactNode,
  ReactPortal,
} from 'react';
import ProposedSolutionActionButtons from './ProposedSolutionActionButtons';

export default function ProposedSolutionDetails() {
  const { data } = useUserContext();
  const location = useLocation();
  const navigate = useNavigate();
  const { solutionRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { successToast, errorToast } = useCustomToast();

  // Get the proposed solutions and current solution from location state
  const { proposedSolutions } = location.state || {};
  const currentSolution = proposedSolutions?.find(
    (solution: ProposedSolution) =>
      solution.proposedSolutionRef === solutionRef,
  );

  const handleBack = () => {
    navigate(-1);
  };

  const handleSaveForLater = () => {
    successToast('Successfully saved for later.');
    navigate(`/${currentAccountType}/dashboard-statements`);
  };

  const handleGenerateProposedProjects = () => {
    if (currentSolution?.proposedSolutionRef) {
      generateProposedProjectsMutation.mutate(currentSolution.proposedSolutionRef);
    }
  };

  if (!currentSolution) {
    return (
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Go Back</span>
          </button>
        </div>

        <div className="-mt-48 flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Solution not found
            </h2>
            <p className="mt-2 text-gray-600">
              The requested solution could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const solutionIndex =
    proposedSolutions?.findIndex(
      (solution: ProposedSolution) =>
        solution.proposedSolutionRef === solutionRef,
    ) + 1;

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">
              Proposed Solution #{solutionIndex}
            </span>
          </button>
        </div>

        {/* Action Buttons */}
        <ProposedSolutionActionButtons
          onSaveForLater={handleSaveForLater}
          onGenerateProposedProjects={handleGenerateProposedProjects}
          isGeneratingProposedProjects={
            generateProposedProjectsMutation.isPending
          }
        />
      </div>

      <div className="mx-auto max-w-4xl space-y-6">
        {/* Solution Image and Title */}
        <div className="overflow-hidden rounded-lg bg-white shadow-sm">
          <div className="aspect-video bg-gray-200">
            <img
              src={solutionThumbnail}
              alt="Project Thumbnail"
              className="h-full w-full object-cover"
            />

            <div className="absolute right-4 top-4">
              <button className="rounded-full bg-white p-2 shadow-md hover:bg-gray-50">
                <Edit size={16} className="text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Proposed Solution Section */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Proposed Solution
            </h2>
            <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200">
              <Edit size={16} className="text-gray-600" />
            </button>
          </div>
          <h3 className="mb-4 text-xl font-bold text-gray-900">
            {currentSolution.goals[0]?.description ||
              'Build A Healthcare Management System'}
          </h3>
          <p className="leading-relaxed text-gray-700">
            {currentSolution.solutionStatement}
          </p>
        </div>

        {/* Proposed Goal & Key Metrics Section */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Proposed Goal & Key Metrics
            </h2>
            <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200">
              <Edit size={16} className="text-gray-600" />
            </button>
          </div>
          <h3 className="mb-4 text-xl font-bold text-gray-900">
            {currentSolution.goals[0]?.description ||
              'Build A Healthcare Management System'}
          </h3>
          <p className="mb-6 leading-relaxed text-gray-700">
            {currentSolution.solutionStatement}
          </p>

          {/* Key Results */}
          <div className="space-y-4">
            {currentSolution.goals[0]?.goalsKeyResults?.map(
              (
                keyResult: {
                  referenceId: Key | null | undefined;
                  keyResultDescription:
                    | string
                    | number
                    | boolean
                    | ReactElement<any, string | JSXElementConstructor<any>>
                    | Iterable<ReactNode>
                    | ReactPortal
                    | null
                    | undefined;
                },
                index: number,
              ) => (
                <div
                  key={keyResult.referenceId}
                  className="border-l-4 border-gray-200 pl-4"
                >
                  <h4 className="font-semibold text-gray-900">
                    Key Result {index + 1}
                  </h4>
                  <p className="text-gray-700">
                    {keyResult.keyResultDescription}
                  </p>
                </div>
              ),
            ) || (
              <>
                <div className="border-l-4 border-gray-200 pl-4">
                  <h4 className="font-semibold text-gray-900">Key Result 1</h4>
                  <p className="text-gray-700">
                    This case study explores how pharmaccess has leveraged
                    innovative approaches to improve healthcare delivery and
                    access to essential services.
                  </p>
                </div>
                <div className="border-l-4 border-gray-200 pl-4">
                  <h4 className="font-semibold text-gray-900">Key Result 2</h4>
                  <p className="text-gray-700">
                    This case study explores how pharmaccess has leveraged
                    innovative approaches to improve healthcare delivery and
                    access to essential services.
                  </p>
                </div>
                <div className="border-l-4 border-gray-200 pl-4">
                  <h4 className="font-semibold text-gray-900">Key Result 3</h4>
                  <p className="text-gray-700">
                    This case study explores how pharmaccess has leveraged
                    innovative approaches to improve healthcare delivery and
                    access to essential services.
                  </p>
                </div>
              </>
            )}
          </div>

          {/* Add Button */}
          <button className="mt-6 flex items-center gap-2 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50">
            <span className="text-lg">+</span>
            <span>Add</span>
          </button>
        </div>

        {/* Second Action Buttons */}
        <ProposedSolutionActionButtons
          onSaveForLater={handleSaveForLater}
          onGenerateProposedProjects={handleGenerateProposedProjects}
          isGeneratingProposedProjects={
            generateProposedProjectsMutation.isPending
          }
        />
      </div>
    </div>
  );
}
