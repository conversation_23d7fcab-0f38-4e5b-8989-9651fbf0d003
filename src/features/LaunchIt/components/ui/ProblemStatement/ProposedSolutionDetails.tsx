import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, PencilLine, Plus } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import {
  ProposedSolution,
  useGenerateProposedProjectsApi,
} from '../../../services/launchItApiRequests';
import { ApiError } from '../../../types';
import { useCustomToast } from '@/hooks/useToast';
import { useAppContext } from '@/context/event/AppEventContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';
import { projectLevels } from '@/features/Categories/data/data';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { useForm } from 'react-hook-form';
import {
  Key,
  ReactElement,
  JSXElementConstructor,
  ReactNode,
  ReactPortal,
} from 'react';
import ProposedSolutionActionButtons from './ProposedSolutionActionButtons';

interface ProjectLevelForm {
  projectLevel: string;
}

export default function ProposedSolutionDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const { solutionRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { successToast, errorToast } = useCustomToast();

  // Form setup for project level selection
  const {
    control,
    watch,
    formState: { errors },
  } = useForm<ProjectLevelForm>({
    defaultValues: {
      projectLevel: '',
    },
  });

  const selectedProjectLevel = watch('projectLevel');

  // API setup
  const generateProposedProjectsApi = useGenerateProposedProjectsApi();

  const generateProposedProjectsMutation = useMutation({
    mutationFn: generateProposedProjectsApi,
    onSuccess: response => {
      console.log('Generate Proposed Projects Success:', response);
      successToast('Proposed projects generated successfully!');

      // Navigate to ProposedProjectsScreen with the response data
      navigate(`/${currentAccountType}/launch/proposed-projects`, {
        state: {
          proposedProjects: response.data,
          selectedSolution: currentSolution,
        },
      });
    },
    onError: (error: ApiError) => {
      console.error('Generate Proposed Projects Error:', error);
      errorToast(
        error?.response?.data?.message ||
          'Failed to generate proposed projects. Please try again.',
      );
    },
  });

  // Get the proposed solutions and current solution from location state
  const { proposedSolutions } = location.state || {};
  const currentSolution = proposedSolutions?.find(
    (solution: ProposedSolution) =>
      solution.proposedSolutionRef === solutionRef,
  );

  const handleBack = () => {
    navigate(-1);
  };

  const handleSaveForLater = () => {
    successToast('Successfully saved for later.');
    navigate(`/${currentAccountType}/dashboard-statements`);
  };

  const handleGenerateProposedProjects = () => {
    if (currentSolution?.proposedSolutionRef && selectedProjectLevel) {
      generateProposedProjectsMutation.mutate({
        proposedSolutionRef: currentSolution.proposedSolutionRef,
        projectLevel: selectedProjectLevel,
      });
    } else if (!selectedProjectLevel) {
      errorToast(
        'Please select a project level before generating proposed projects.',
      );
    }
  };

  if (!currentSolution) {
    return (
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Go Back</span>
          </button>
        </div>

        <div className="-mt-48 flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Solution not available
            </h2>
            <p className="mt-2 text-gray-600">
              The requested solution could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const solutionIndex =
    proposedSolutions?.findIndex(
      (solution: ProposedSolution) =>
        solution.proposedSolutionRef === solutionRef,
    ) + 1;

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">
              Proposed Solution #{solutionIndex}
            </span>
          </button>
        </div>

        <div className="-mt-7">
          {/* Action Buttons */}
          <ProposedSolutionActionButtons
            onSaveForLater={handleSaveForLater}
            onGenerateProposedProjects={handleGenerateProposedProjects}
            isGeneratingProposedProjects={
              generateProposedProjectsMutation.isPending
            }
          />
        </div>
      </div>

      <div className="mx-auto max-w-4xl space-y-6">
        <div className="flex w-full items-center justify-center">
          <img
            src={solutionThumbnail}
            alt="Project Thumbnail"
            className="h-64 w-80 rounded-2xl object-cover"
          />
        </div>

        {/* <div className="absolute right-4 top-4">
              <button className="rounded-full bg-white p-2 shadow-md hover:bg-gray-50">
                <PencilLine size={16} className="text-gray-600" />
              </button>
            </div> */}

        {/* Proposed Solution Section */}
        {/* <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="bg-darkGray mb-4 flex items-center justify-between rounded-t-lg p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Solution
            </div>
            <button className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200">
              Edit
              <PencilLine size={20} />
            </button>
          </div>
          <div className="px-2 text-xl font-bold text-gray-900 capitalize">
            {currentSolution.goals[0]?.description ||
              ''}
          </div>
          <p className="p-2 leading-relaxed text-gray-700">
            {currentSolution.solutionStatement}
          </p>
        </div> */}

        {/* Proposed Goal & Key Metrics Section */}
        <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="mb-4 flex items-center justify-between rounded-t-lg bg-darkGray p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Goal & Key Metrics
            </div>
            <button className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200">
              Edit
              <PencilLine size={20} />
            </button>
          </div>
          <div className="px-2 text-xl font-bold capitalize text-gray-900">
            {currentSolution.goals[0]?.description || ''}
          </div>
          <p className="p-2 leading-relaxed text-gray-700">
            {currentSolution.solutionStatement}
          </p>

          <div className="px-2">
            {currentSolution.goals[0]?.goalsKeyResults.map(
              (
                keyResult: {
                  referenceId: Key | null | undefined;
                  keyResultDescription:
                    | string
                    | number
                    | boolean
                    | ReactElement<any, string | JSXElementConstructor<any>>
                    | Iterable<ReactNode>
                    | ReactPortal
                    | null
                    | undefined;
                },
                index: number,
              ) => (
                <div
                  key={keyResult.referenceId}
                  className="mb-2 rounded-xl border border-gray-200 bg-peachOne p-2"
                >
                  <div className="mb-4 w-fit rounded border border-gray-200 bg-white px-4 py-1.5 font-medium">
                    Key Result {index + 1}
                  </div>
                  <p className="leading-relaxed text-gray-700">
                    {keyResult.keyResultDescription}
                  </p>
                </div>
              ),
            )}

            <div className="mb-2 w-fit rounded-xl border border-gray-200 bg-peachOne p-2">
              <button className="flex items-center gap-2 rounded border border-gray-200 bg-white px-4 py-1.5 font-medium hover:bg-grayNineTeen">
                Add
                <Plus size={20} className="-mt-1" />
              </button>
            </div>
          </div>
        </div>

        {/* Project Level Selection */}
        <div className="mt-8 rounded-lg border border-gray-200 bg-white p-6">
          <p className="mb-4 text-lg font-semibold text-gray-900">
            Select Project Level
          </p>
          <FormSelectBox<ProjectLevelForm>
            labelName="Choose the minimum project level required for the proposed projects"
            options={projectLevels}
            optionsArr={projectLevels}
            control={control}
            name="projectLevel"
            errors={errors}
            placeholder="Select Project Level"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end">
          <ProposedSolutionActionButtons
            onSaveForLater={handleSaveForLater}
            onGenerateProposedProjects={handleGenerateProposedProjects}
            isGeneratingProposedProjects={
              generateProposedProjectsMutation.isPending
            }
          />
        </div>
      </div>
    </div>
  );
}
