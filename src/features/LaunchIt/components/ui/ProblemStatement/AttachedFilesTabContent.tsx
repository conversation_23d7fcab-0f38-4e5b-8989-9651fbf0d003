import { Download, FileText, File } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProblemStatementResponse } from '../../../types';

interface AttachedFilesTabContentProps {
  data: ProblemStatementResponse;
}

export default function AttachedFilesTabContent({ data }: AttachedFilesTabContentProps) {
  const handleDownload = (file: any) => {
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FileText className="w-8 h-8 text-red-500" />;
    } else if (fileType.includes('doc') || fileType.includes('docx')) {
      return <FileText className="w-8 h-8 text-blue-500" />;
    }
    return <File className="w-8 h-8 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!data.attachedFiles || data.attachedFiles.length === 0) {
    return (
      <div className="text-center py-12">
        <File className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No files attached</h3>
        <p className="text-gray-500">No files were uploaded with this problem statement.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">
        Attached Files ({data.attachedFiles.length})
      </h2>
      
      <div className="grid gap-4">
        {data.attachedFiles.map((file) => (
          <div
            key={file.id}
            className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                {getFileIcon(file.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {file.name}
                </p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(file.size)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => handleDownload(file)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-sm"
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* File Preview Section */}
      <div className="mt-8">
        <h3 className="text-md font-semibold text-gray-800 mb-4">File Preview</h3>
        <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
          <div className="text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600 text-sm">
              File preview is not available for this file type.
            </p>
            <p className="text-gray-500 text-xs mt-1">
              Click download to view the file content.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
