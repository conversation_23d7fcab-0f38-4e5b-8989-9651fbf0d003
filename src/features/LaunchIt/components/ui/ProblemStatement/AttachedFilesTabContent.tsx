import { Download, FileText, File } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProblemStatementResponse, AttachedFile } from '../../../types';

interface AttachedFilesTabContentProps {
  data: ProblemStatementResponse;
}

export default function AttachedFilesTabContent({
  data,
}: AttachedFilesTabContentProps) {
  const handleDownload = (file: AttachedFile) => {
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FileText className="h-8 w-8 text-red-500" />;
    } else if (fileType.includes('doc') || fileType.includes('docx')) {
      return <FileText className="h-8 w-8 text-blue-500" />;
    }
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!data.attachedFiles || data.attachedFiles.length === 0) {
    return (
      <div className="py-12 text-center">
        <File className="mx-auto mb-4 h-16 w-16 text-gray-300" />
        <h3 className="mb-2 text-lg font-medium text-gray-900">
          No files attached
        </h3>
        <p className="text-gray-500">
          No files were uploaded with this problem statement.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4">
        {data.attachedFiles.map(file => (
          <div
            key={file.id}
            className="flex items-center justify-between rounded-lg border border-gray-200 p-4 transition-colors hover:bg-gray-50"
          >
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">{getFileIcon(file.type)}</div>
              <div className="min-w-0 flex-1">
                <p className="truncate text-sm font-medium text-gray-900">
                  {file.name}
                </p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(file.size)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                onClick={() => handleDownload(file)}
                className="inline-flex items-center border border-gray-300 bg-white px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
