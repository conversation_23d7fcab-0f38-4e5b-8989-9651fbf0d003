import { ReactNode } from 'react';

interface TabNavigationProps {
  activeTab: 'description' | 'attachedFiles';
  onTabChange: (tab: 'description' | 'attachedFiles') => void;
  children: ReactNode;
}

export default function TabNavigation({ activeTab, onTabChange, children }: TabNavigationProps) {
  const activeClass = 'border-b bg-white border-primary shadow-sm';

  return (
    <div className="pb-[30px] text-[16px] md:pr-8">
      <div className="flex gap-2">
        <div
          className={`my-4 w-[200px] max-w-full cursor-pointer items-stretch 
          justify-center px-5 py-2.5 text-center font-medium leading-6 text-neutral-800 sm:my-8 ${
            activeTab === 'description' ? activeClass : ''
          }`}
          onClick={() => onTabChange('description')}
        >
          Description
        </div>
        <div
          className={`my-4 w-[200px] max-w-full cursor-pointer items-stretch 
          justify-center px-5 py-2.5 text-center font-medium leading-6 text-neutral-800 sm:my-8 ${
            activeTab === 'attachedFiles' ? activeClass : ''
          }`}
          onClick={() => onTabChange('attachedFiles')}
        >
          Attached Files
        </div>
      </div>
      {children}
    </div>
  );
}
