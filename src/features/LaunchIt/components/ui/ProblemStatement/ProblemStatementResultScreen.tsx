import { useState } from 'react';
import { ProblemStatementResponse } from '../../../types';
import ProblemStatementActionButtons from './ProblemStatementActionButtons';
import TabNavigation from './TabNavigation';
import DescriptionTabContent from './DescriptionTabContent';
import AttachedFilesTabContent from './AttachedFilesTabContent';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '@/context/event/AppEventContext';
import { useCustomToast } from '@/hooks/useToast';
import { useGenerateProposedSolutions } from '../../../hooks/apiQueryHooks/launchItQueryHooks';
import { GenerateProposedSolutionsResponse } from '../../../services/launchItApiRequests';

interface ProblemStatementResultScreenProps {
  data: ProblemStatementResponse | null;
  onBack: () => void;
}

export default function ProblemStatementResultScreen({
  data,
}: ProblemStatementResultScreenProps) {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();

  const [activeTab, setActiveTab] = useState<'description' | 'attachedFiles'>(
    'description',
  );

  const { successToast, errorToast } = useCustomToast();

  const generateProposedSolutionsMutation = useGenerateProposedSolutions({
    onSuccess: (response: GenerateProposedSolutionsResponse) => {
      successToast('Proposed solutions generated successfully!');
      // Navigate to proposed solutions screen with the data
      navigate('/launch/proposed-solutions', {
        state: {
          proposedSolutions: response.data,
          problemStatementTitle: data?.title,
        },
      });
    },
    onError: (error: any) => {
      console.error('Error generating proposed solutions:', error);
      errorToast('Failed to generate proposed solutions. Please try again.');
    },
  });

  const handleSaveForLater = () => {
    successToast('Successfully saved for later.');
    // Navigate to dashboard-statements
    navigate(`/${currentAccountType}/dashboard-statements`);
  };

  const handleGenerateProposedSolutions = () => {
    if (data?.problemStatementRef) {
      generateProposedSolutionsMutation.mutate(data.problemStatementRef);
    }
  };

  if (!data) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">No problem statement data available.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col px-12">
      <div className="flex justify-between">
        {/* Created By Section */}
        <div className="flex items-center gap-3 pt-6">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
            {data.createdBy.avatar ? (
              <img
                src={data.createdBy.avatar}
                alt={`${data.createdBy.firstName} ${data.createdBy.lastName}`}
                className="h-full w-full rounded-full object-cover"
              />
            ) : (
              <span className="text-sm font-medium text-gray-600">
                {data.createdBy.firstName.charAt(0)}
                {data.createdBy.lastName.charAt(0)}
              </span>
            )}
          </div>
          <div>
            <p className="text-sm text-grayFourteen">Created by:</p>
            <p className="font-medium">
              {data.createdBy.firstName} {data.createdBy.lastName}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <ProblemStatementActionButtons
          onSaveForLater={handleSaveForLater}
          onGenerateProposedSolutions={handleGenerateProposedSolutions}
          isGeneratingProposedSolutions={
            generateProposedSolutionsMutation.isPending
          }
        />
      </div>

      <div className="w-full max-w-screen-lg">
        <TabNavigation activeTab={activeTab} onTabChange={setActiveTab}>
          {activeTab === 'description' ? (
            <DescriptionTabContent data={data} />
          ) : (
            <AttachedFilesTabContent data={data} />
          )}
        </TabNavigation>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end">
        <ProblemStatementActionButtons
          onSaveForLater={handleSaveForLater}
          onGenerateProposedSolutions={handleGenerateProposedSolutions}
          isGeneratingProposedSolutions={
            generateProposedSolutionsMutation.isPending
          }
        />
      </div>
    </div>
  );
}
