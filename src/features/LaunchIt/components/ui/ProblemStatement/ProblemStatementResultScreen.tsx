import { useState } from 'react';
import { ProblemStatementResponse } from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import TabNavigation from './TabNavigation';
import DescriptionTabContent from './DescriptionTabContent';
import AttachedFilesTabContent from './AttachedFilesTabContent';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '@/context/event/AppEventContext';
import { useCustomToast } from '@/hooks/useToast';

interface ProblemStatementResultScreenProps {
  data: ProblemStatementResponse | null;
  onBack: () => void;
}

export default function ProblemStatementResultScreen({
  data,
}: ProblemStatementResultScreenProps) {
  const navigate = useNavigate();
  const { currentAccountType } = useAppContext();

  const [activeTab, setActiveTab] = useState<'description' | 'attachedFiles'>(
    'description',
  );

  const { successToast } = useCustomToast();
  const handleSaveForLater = () => {
    successToast('Successfully saved for later.');
    // Navigate to dashboard-statements
    navigate(`/${currentAccountType}/dashboard-statements`);
  };

  const handleGenerateProposedSolutions = () => {
    // Implementation for generating proposed solutions
    console.log('Generate proposed solutions clicked');
  };

  if (!data) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">No problem statement data available.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col px-12">
      <div className="flex justify-between">
        {/* Created By Section */}
        <div className="flex items-center gap-3 py-6">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
            {data.createdBy.avatar ? (
              <img
                src={data.createdBy.avatar}
                alt={`${data.createdBy.firstName} ${data.createdBy.lastName}`}
                className="h-full w-full rounded-full object-cover"
              />
            ) : (
              <span className="text-sm font-medium text-gray-600">
                {data.createdBy.firstName.charAt(0)}
                {data.createdBy.lastName.charAt(0)}
              </span>
            )}
          </div>
          <div>
            <p className="text-sm text-grayFourteen">Created by:</p>
            <p className="font-medium">
              {data.createdBy.firstName} {data.createdBy.lastName}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-4 pt-6 sm:flex-row">
          <Button
            onClick={handleSaveForLater}
            className="text-darkGray w-full border border-black bg-white px-6 py-3 hover:border-primary hover:text-primary sm:w-auto"
          >
            Save For Later
          </Button>
          <Button
            onClick={handleGenerateProposedSolutions}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 sm:w-auto"
          >
            Generate Proposed Solutions →
          </Button>
        </div>
      </div>

      <div className="w-full max-w-screen-lg">
        <TabNavigation activeTab={activeTab} onTabChange={setActiveTab}>
          {activeTab === 'description' ? (
            <DescriptionTabContent data={data} />
          ) : (
            <AttachedFilesTabContent data={data} />
          )}
        </TabNavigation>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end">
        <div className="flex flex-col gap-4 pt-6 sm:flex-row">
          <Button
            onClick={handleSaveForLater}
            className="text-darkGray w-full border border-black bg-white px-6 py-3 hover:border-primary hover:text-primary sm:w-auto"
          >
            Save For Later
          </Button>
          <Button
            onClick={handleGenerateProposedSolutions}
            className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 sm:w-auto"
          >
            Generate Proposed Solutions →
          </Button>
        </div>
      </div>
    </div>
  );
}
