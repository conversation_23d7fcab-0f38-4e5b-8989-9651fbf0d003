import { useState } from 'react';
import { ProblemStatementResponse } from '../../../types';
import TabNavigation from './TabNavigation';
import DescriptionTabContent from './DescriptionTabContent';
import AttachedFilesTabContent from './AttachedFilesTabContent';

interface ProblemStatementResultScreenProps {
  data: ProblemStatementResponse | null;
  onBack: () => void;
}

export default function ProblemStatementResultScreen({
  data,
}: ProblemStatementResultScreenProps) {
  const [activeTab, setActiveTab] = useState<'description' | 'attachedFiles'>(
    'description',
  );

  if (!data) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">No problem statement data available.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center px-12">
      <div className="w-full max-w-screen-lg">
        <TabNavigation activeTab={activeTab} onTabChange={setActiveTab}>
          {activeTab === 'description' ? (
            <DescriptionTabContent data={data} />
          ) : (
            <AttachedFilesTabContent data={data} />
          )}
        </TabNavigation>
      </div>
    </div>
  );
}
