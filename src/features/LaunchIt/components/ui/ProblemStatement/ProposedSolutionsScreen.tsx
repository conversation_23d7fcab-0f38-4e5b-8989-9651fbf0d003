import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProposedSolution } from '../../../services/launchItApiRequests';
import { useUserContext } from '@/context/user/UserContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';

export default function ProposedSolutionsScreen() {
  const { data } = useUserContext();
  const location = useLocation();
  const navigate = useNavigate();

  const { proposedSolutions } =
    (location.state as {
      proposedSolutions: ProposedSolution[];
    }) || {};

  const handleBack = () => {
    navigate(-1);
  };

  const handleViewProposedSolution = (solutionRef: string) => {
    // Navigate to individual proposed solution view
    navigate(
      `/${data?.user?.accountType?.toLowerCase()}/launch/proposed-solutions/${solutionRef}`,
      {
        state: { proposedSolutions, solutionRef },
      },
    );
  };

  if (!proposedSolutions || proposedSolutions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">No proposed solutions available.</p>
        <Button onClick={handleBack} className="mt-4">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="">
      <div className="flex items-center gap-2 px-3.5">
        <button
          onClick={handleBack}
          className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
        >
          <ArrowLeft size={24} className="-mt-0.5 text-primary" />
          <span className="text-lg font-medium">
            Proposed Solutions For You
          </span>
        </button>
      </div>

      <div className="flex flex-col items-center px-12">
        <div className="my-8 w-full max-w-screen-md rounded-md border border-dashed border-grayNine bg-white p-4">
          <p className="text-darkGray text-center">
            {data?.user?.firstName
              ? `${data?.user?.firstName}, now let's select one of ${proposedSolutions.length} proposed solutions below.`
              : `Now let's select one of ${proposedSolutions.length} proposed solutions below.`}
          </p>
        </div>
      </div>

      {/* Solutions Grid */}
      <div className="grid grid-cols-1 gap-6 px-4 md:grid-cols-2 lg:grid-cols-4">
        {proposedSolutions.map((solution, index) => (
          <div
            key={solution.proposedSolutionRef}
            className="relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
          >
            {/* Proposed Label */}
            <div className="absolute right-0 top-0 z-10">
              <span className="bg-greenOne px-2 py-1 text-xs font-medium text-white">
                Proposed #{index + 1}
              </span>
            </div>

            {/* Image */}
            <div className="aspect-video bg-gray-100">
              {/* {solution.imageUrl} */}
              <img
                src={solutionThumbnail}
                alt={solution.goals[0].description}
                className="h-full w-full object-cover"
              />
            </div>

            {/* Content */}
            <div className="p-4">
              <h3 className="mb-2 text-sm font-bold text-gray-900">
                {solution.goals[0].description || '[Proposed Project Name]'}
              </h3>

              <div className="text-darkGray mb-3 space-y-2 text-[10px] font-semibold">
                <p className="rounded bg-grayNineTeen p-2">
                  <span className="font-medium">Goals Categories:</span>{' '}
                  {solution.categories
                    .map(category => category.categoryName)
                    ?.join(', ') ||
                    'Good Health & Well-Being, Quality Education'}
                </p>
                <p className="rounded bg-grayNineTeen p-2">
                  <span className="font-medium">Institution:</span>{' '}
                  {/* {solution.institution}*/}University of Lagos
                </p>
                <p className="rounded bg-grayNineTeen p-2">
                  <span className="font-medium">Country Focus:</span>{' '}
                  {solution.focusCountries?.join(', ') || 'Nigeria'}
                </p>
              </div>

              <Button
                onClick={() =>
                  handleViewProposedSolution(solution.proposedSolutionRef)
                }
                className="w-full bg-primary text-sm text-white hover:bg-primary/80"
              >
                View Proposed Solution
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
