import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Arrow<PERSON>eft, ChevronLeft, ChevronRight } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { ProposedSolution } from '../../../services/launchItApiRequests';

interface ProposedSolutionsScreenProps {
  proposedSolutions?: ProposedSolution[];
  problemStatementTitle?: string;
}

export default function ProposedSolutionsScreen() {
  const location = useLocation();
  const navigate = useNavigate();
  
  const { proposedSolutions, problemStatementTitle } = location.state as {
    proposedSolutions: ProposedSolution[];
    problemStatementTitle: string;
  } || {};

  const handleBack = () => {
    navigate(-1);
  };

  const handleViewProposedSolution = (solutionId: string) => {
    // Navigate to individual proposed solution view
    console.log('View proposed solution:', solutionId);
  };

  if (!proposedSolutions || proposedSolutions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-gray-500">No proposed solutions available.</p>
        <Button onClick={handleBack} className="mt-4">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 py-8">
      {/* Header */}
      <div className="mb-8 flex items-center gap-4">
        <button
          onClick={handleBack}
          className="flex items-center gap-2 text-primary hover:text-primary/80"
        >
          <ArrowLeft size={20} />
          <span className="text-lg font-medium">Proposed Solutions For You</span>
        </button>
      </div>

      {/* Subtitle */}
      <div className="mb-8">
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
          <p className="text-center text-gray-600">
            {problemStatementTitle ? 
              `${problemStatementTitle.split(' ').slice(0, 2).join(' ')}, now let's select one of ${proposedSolutions.length} proposed solutions below.` :
              `Now let's select one of ${proposedSolutions.length} proposed solutions below.`
            }
          </p>
        </div>
      </div>

      {/* Solutions Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {proposedSolutions.map((solution, index) => (
          <div
            key={solution.id}
            className="relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
          >
            {/* Proposed Label */}
            <div className="absolute left-3 top-3 z-10">
              <span className="rounded bg-green-500 px-2 py-1 text-xs font-medium text-white">
                Proposed #{index + 1}
              </span>
            </div>

            {/* Image */}
            <div className="aspect-video bg-gray-100">
              {solution.imageUrl ? (
                <img
                  src={solution.imageUrl}
                  alt={solution.title}
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center">
                  <div className="h-16 w-16 rounded-full bg-gray-300"></div>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="p-4">
              <h3 className="mb-2 font-semibold text-gray-900">
                {solution.title || '[Proposed Project Name]'}
              </h3>
              
              <div className="mb-3 space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">Goals Categories:</span>{' '}
                  {solution.goalsCategories?.join(', ') || 'Good Health & Well-Being, Quality Education'}
                </p>
                <p>
                  <span className="font-medium">Institution:</span>{' '}
                  {solution.institution || 'University of Port Harcourt'}
                </p>
                <p>
                  <span className="font-medium">Country Focus:</span>{' '}
                  {solution.countryFocus?.join(', ') || 'Nigeria, Ghana, Albania...'}
                </p>
              </div>

              <Button
                onClick={() => handleViewProposedSolution(solution.id)}
                className="w-full bg-primary text-white hover:bg-primary/80"
              >
                View Proposed Solution
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <div className="mt-8 flex justify-center gap-4">
        <button
          className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-600 hover:bg-gray-50"
          disabled={true}
        >
          <ChevronLeft size={20} />
        </button>
        <button
          className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-600 hover:bg-gray-50"
          disabled={proposedSolutions.length <= 4}
        >
          <ChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
