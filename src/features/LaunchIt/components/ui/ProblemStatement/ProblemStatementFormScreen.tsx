import { Submit<PERSON>and<PERSON> } from 'react-hook-form';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useFormDataAndApiMutateHandler } from '@/hooks/useFormDataAndApiMutateHandler';
import { Helper } from '@/utils/helpers';

import Form from '@/components/forms/Form';
import FormTextAreaBox from '@/components/forms/FormTextAreaBox';
import FormFileInputBox from '@/components/forms/FormFileInputBox';
import FormSelectBox from '@/components/forms/FormSelectBox';
import FormInputBox from '@/components/forms/FormInputBox';
import FormRadioButton from '@/components/forms/FormRadioButton';
import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

import { createProblemStatementSchema } from '../../../lib/yup/validations';
import { CreateProblemStatementFormData } from '../../../types';
import { budgetOptions } from '../../../data/constants';
import { useCreateProblemStatementContext } from '../../../context/CreateProblemStatementContext';

interface ProblemStatementFormScreenProps {
  onSubmit: (data: CreateProblemStatementFormData) => void;
  userName?: string;
}

export default function ProblemStatementFormScreen({ onSubmit, userName }: ProblemStatementFormScreenProps) {
  const { formData, updateFormData } = useCreateProblemStatementContext();
  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();

  const countryOptions = Helper.createListForCountries(countries?.data?.countries || []);

  const {
    handleSubmit,
    register,
    control,
    watch,
    formState: { errors, isDirty, isValid },
  } = useFormDataAndApiMutateHandler<CreateProblemStatementFormData, any>(
    createProblemStatementSchema,
    () => ({ mutate: () => {}, isLoading: false }),
    {
      defaultValues: {
        description: formData.description || '',
        detailedDescriptionFile: formData.detailedDescriptionFile || null,
        focusCountries: formData.focusCountries || [],
        budget: formData.budget || '',
        status: formData.status || 'public',
        videoLink: formData.videoLink || '',
      },
    }
  );

  const handleFormSubmit: SubmitHandler<CreateProblemStatementFormData> = (data) => {
    updateFormData(data);
    onSubmit(data);
  };

  const descriptionValue = watch('description') || '';
  const characterCount = descriptionValue.length;

  return (
    <div className="flex flex-col items-center px-12">
      <div className="my-8 w-full max-w-screen-md rounded-md border border-dashed border-grayNine px-12 py-4 text-center">
        <p className="">
          {userName}, now let's understand the challenge.
        </p>
      </div>

      <div className="w-full max-w-screen-md">
        <Form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Description Field */}
          <div>
            <FormTextAreaBox
              name="description"
              labelName="Describe The Problem"
              placeholder="Tell us as much as possible"
              errors={errors}
              registerHanlder={() => register('description')}
              className="input mt-2 min-h-[200px] resize-none"
              rows={8}
              maxChar={5000}
            />
            <div className="mt-1 text-right text-sm text-gray-500">
              {characterCount}/5000
            </div>
          </div>

          {/* File Upload */}
          <div>
            <FormFileInputBox
              name="detailedDescriptionFile"
              labelName="Upload Detailed Description (Docx, Pdf) (Recommended)"
              control={control}
              errors={errors}
              accept=".pdf,.doc,.docx"
            />
          </div>

          {/* Focus Countries */}
          <div>
            <FormSelectBox
              name="focusCountries"
              labelName="Focus Countries (up to 5)"
              placeholder="Select Countries"
              control={control}
              errors={errors}
              optionsArr={countryOptions}
              isMulti
              isLoading={isLoadingCountries}
            />
          </div>

          {/* Budget */}
          <div>
            <FormSelectBox
              name="budget"
              labelName="Budget (USD)"
              placeholder="Enter Budget in USD"
              control={control}
              errors={errors}
              optionsArr={budgetOptions}
            />
          </div>

          {/* Status Radio Buttons */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Status <span className="text-primary">*</span>
            </label>
            <div className="space-y-2">
              <div className="flex items-center">
                <FormRadioButton
                  id="status-private"
                  name="status"
                  value="private"
                  registerHanlder={() => register('status')}
                  className="mr-2"
                />
                <label htmlFor="status-private" className="text-sm">Private</label>
              </div>
              <div className="flex items-center">
                <FormRadioButton
                  id="status-public"
                  name="status"
                  value="public"
                  registerHanlder={() => register('status')}
                  className="mr-2"
                />
                <label htmlFor="status-public" className="text-sm">Public</label>
              </div>
            </div>
          </div>

          {/* Video Link */}
          <div>
            <FormInputBox
              name="videoLink"
              labelName="Upload Description Video Link (Youtube)"
              placeholder="Upload Video Link"
              type="url"
              errors={errors}
              registerHanlder={() => register('videoLink')}
              className="input mt-2"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-center pt-6">
            <Button
              type="submit"
              disabled={!isDirty || !isValid}
              className="group w-full max-w-[400px] border border-primary bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              <p className="text-[14px] font-[500] sm:text-[16px]">
                Refine Problem Statement →
              </p>
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
}
