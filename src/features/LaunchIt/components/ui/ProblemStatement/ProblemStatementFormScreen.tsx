import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { Helper } from '@/utils/helpers';
import { useCreateLaunchItProblemStatement } from '@/features/LaunchIt/hooks/apiQueryHooks/launchItQueryHooks';
import { useCustomToast } from '@/hooks/useToast';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

import Form from '@/components/forms/Form';
import FormTextAreaBox from '@/components/forms/FormTextAreaBox';
import FormFileInputBox from '@/components/forms/FormFileInputBox';
import FormSelectBox from '@/components/forms/FormSelectBox';
import FormInputBox from '@/components/forms/FormInputBox';
import FormRadioButton from '@/components/forms/FormRadioButton';
import Button from '@/components/ui/ButtonComponent';

import { createProblemStatementSchema } from '../../../lib/yup/validations';
import {
  CreateProblemStatementFormData,
  ProblemStatementApiResponse,
  ApiError,
} from '../../../types';
import { LaunchItProblemStatementResponse } from '../../../services/launchItApiRequests';

import { useCreateProblemStatementContext } from '../../../context/CreateProblemStatementContext';
import { ArrowRight } from 'lucide-react';

interface ProblemStatementFormScreenProps {
  onSubmit: (data: ProblemStatementApiResponse) => void;
  userName?: string;
}

export default function ProblemStatementFormScreen({
  onSubmit,
  userName,
}: ProblemStatementFormScreenProps) {
  const { formData, updateFormData } = useCreateProblemStatementContext();
  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const { successToast, errorToast } = useCustomToast();

  const { mutate: createProblemStatement, isPending: isSubmitting } =
    useCreateLaunchItProblemStatement({
      onSuccess: (response: LaunchItProblemStatementResponse) => {
        successToast(
          response?.message || 'Problem statement created successfully!',
        );
        // Call the parent onSubmit with the API response
        onSubmit(response?.data);
      },
      onError: (error: ApiError) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to create problem statement',
        );
      },
    });

  const {
    handleSubmit,
    register,
    control,
    watch,
    formState: { errors, isDirty, isValid },
  } = useForm<CreateProblemStatementFormData>({
    resolver: yupResolver(createProblemStatementSchema),
    mode: 'onChange',
    defaultValues: {
      description: formData.description || '',
      detailedDescriptionFile: formData.detailedDescriptionFile || null,
      focusCountries: formData.focusCountries || [],
      budget: formData.budget || null,
      status: formData.status || 'public',
      videoLink: formData.videoLink || undefined,
    },
  });

  const handleFormSubmit: SubmitHandler<
    CreateProblemStatementFormData
  > = data => {
    updateFormData(data);
    // Call the API to create the problem statement
    createProblemStatement(data);
  };

  const descriptionValue = watch('description') || '';
  const characterCount = descriptionValue.length;

  return (
    <div className="flex flex-col items-center px-12">
      <div className="my-8 w-full max-w-screen-md rounded-md border border-dashed border-grayNine bg-white px-12 py-4 text-center">
        <p className="text-darkGray">
          {userName}, now let's understand the challenge.
        </p>
      </div>

      {isSubmitting ? (
        <div className="flex flex-col items-center justify-center px-12 py-24">
          <div className="flex flex-col items-center space-y-4">
            <Spinner className="h-12 w-12 border-4" />
            <p className="text-lg font-medium text-gray-600">
              Hang tight, almost there...
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full max-w-screen-md">
          <Form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Description Field */}
            <div>
              <FormTextAreaBox
                name="description"
                labelName="Describe The Problem"
                placeholder="Tell us as much as possible"
                errors={errors}
                registerHanlder={() => register('description')}
                className="input mt-2 min-h-[200px] resize-none"
                rows={8}
                maxChar={5000}
                showCharCount={false}
              />
              <div className="mt-1 text-right text-sm text-gray-500">
                {characterCount}/5000
              </div>
            </div>

            {/* File Upload */}
            <div>
              <FormFileInputBox
                name="detailedDescriptionFile"
                labelName="Upload Detailed Description (Docx, Pdf) (Recommended)"
                placeholder="Upload File"
                control={control}
                errors={errors}
                accept=".pdf,.doc,.docx"
                required={false}
              />
            </div>

            {/* Focus Countries */}
            <div>
              <FormSelectBox
                name="focusCountries"
                labelName="Focus Countries (up to 5)"
                placeholder="Select Countries"
                control={control}
                errors={errors}
                options={Helper.createCountriesOptionsArray(
                  countries?.data || { countries: [] },
                )}
                optionsArr={Helper.createCountriesOptionsArray(
                  countries?.data || { countries: [] },
                )}
                isMulti
                isLoading={isLoadingCountries}
              />
            </div>

            {/* Budget */}
            <div>
              <FormInputBox
                name="budget"
                labelName="Budget (USD)"
                placeholder="Enter Budget in USD"
                registerHanlder={() => register('budget')}
                errors={errors}
                type="number"
                min="0"
                step="0.01"
              />
            </div>

            {/* Status Radio Buttons */}
            <div>
              <p className="mb-2">Status</p>
              <div className="mb-2 flex flex-col flex-wrap gap-[15px] md:flex-row">
                <FormRadioButton<CreateProblemStatementFormData>
                  labelName={<p>Private</p>}
                  name="status"
                  errors={errors}
                  registerHanlder={() => register('status')}
                  value="private"
                />
              </div>
              <div className="flex flex-col flex-wrap gap-[15px] md:flex-row">
                <FormRadioButton<CreateProblemStatementFormData>
                  labelName={<p>Public</p>}
                  name="status"
                  errors={errors}
                  registerHanlder={() => register('status')}
                  value="public"
                />
              </div>
            </div>

            {/* YouTube Video Link - Only show when status is public */}
            {watch('status') === 'public' && (
              <div>
                <FormInputBox
                  name="videoLink"
                  labelName="Upload Description Video Link (YouTube)"
                  placeholder="Upload Video Link"
                  type="url"
                  errors={errors}
                  registerHanlder={() => register('videoLink')}
                  className="input mt-2"
                />
              </div>
            )}

            {/* Submit Button */}
            <div className="pt-6">
              <Button
                type="submit"
                disabled={!isDirty || !isValid}
                className="group flex h-11 w-full max-w-[295px] items-center gap-3 rounded-lg bg-primary text-white disabled:cursor-not-allowed disabled:bg-grayTwentyOne"
              >
                <p className="text-[14px] font-[500] sm:text-[16px]">
                  Refine Problem Statement
                </p>
                <ArrowRight size={18} className="-mt-0.5" />
              </Button>
            </div>
          </Form>
        </div>
      )}
    </div>
  );
}
