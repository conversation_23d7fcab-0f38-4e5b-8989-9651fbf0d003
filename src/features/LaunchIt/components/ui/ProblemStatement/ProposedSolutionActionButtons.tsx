import Button from '@/components/ui/ButtonComponent';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

interface ProposedSolutionActionButtonsProps {
  onSaveForLater: () => void;
  onGenerateProposedProjects: () => void;
  isGeneratingProposedProjects?: boolean;
}

export default function ProposedSolutionActionButtons({
  onSaveForLater,
  onGenerateProposedProjects,
  isGeneratingProposedProjects = false,
}: ProposedSolutionActionButtonsProps) {
  return (
    <div className="flex flex-col gap-4 pt-6 sm:flex-row">
      <Button
        onClick={onSaveForLater}
        className="text-darkGray w-full border border-black bg-white px-6 py-3 hover:border-primary hover:text-primary sm:w-auto"
      >
        Save For Later
      </Button>
      <Button
        onClick={onGenerateProposedProjects}
        disabled={isGeneratingProposedProjects}
        className="w-full border border-primary bg-primary px-6 py-3 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50 sm:w-auto"
      >
        {isGeneratingProposedProjects ? (
          <div className="flex items-center gap-2">
            <Spinner size="sm" />
            <span>Hang tight, almost there...</span>
          </div>
        ) : (
          'Generate Proposed Projects →'
        )}
      </Button>
    </div>
  );
}
