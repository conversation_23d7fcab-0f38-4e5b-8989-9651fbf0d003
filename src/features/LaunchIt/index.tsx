import { useNavigate } from 'react-router-dom';
import { overviewCards } from './data/constants';
import CardOption from './components/ui/CardOption';
import { useUserContext } from '@/context/user/UserContext';

export default function LaunchItOverview() {
  const { data } = useUserContext();
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center px-12">
      <div className="my-8 w-3/4 max-w-screen-md rounded-md border border-dashed border-grayNine px-12 py-2.5 text-center">
        <p className="text-xl">Hi {`${data?.user?.firstName}`}!</p>
        <p className="text-darkGray mt-2 font-semibold">
          Ready to bring your idea to life?
        </p>
      </div>

      <div className="flex justify-center">
        <div className="flex flex-wrap items-center justify-center gap-6">
          {overviewCards.map((card, index) => (
            <CardOption
              key={index}
              {...card}
              onClick={() =>
                card.route
                  ? navigate(card.route)
                  : console.warn('No route defined for', card.title)
              }
            />
          ))}
        </div>
      </div>
    </div>
  );
}
