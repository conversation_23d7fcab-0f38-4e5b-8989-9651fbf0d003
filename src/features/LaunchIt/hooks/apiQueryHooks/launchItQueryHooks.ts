import { useMutation } from '@tanstack/react-query';
import { useCreateLaunchItProblemStatementApi } from '../../services/launchItApiRequests';

export const useCreateLaunchItProblemStatement = (options = {}) => {
  const createProblemStatement = useCreateLaunchItProblemStatementApi();
  return useMutation(createProblemStatement, {
    mutationKey: ['CREATE_LAUNCH_IT_PROBLEM_STATEMENT'],
    ...options,
  });
};
