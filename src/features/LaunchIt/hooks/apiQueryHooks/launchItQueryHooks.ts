import { useMutation } from '@tanstack/react-query';
import {
  useCreateLaunchItProblemStatementApi,
  useGenerateProposedSolutionsApi,
  GenerateProposedSolutionsResponse,
  useGenerateAutomationSummaryApi,
  AutomationSummaryResponse,
} from '../../services/launchItApiRequests';

export const useCreateLaunchItProblemStatement = (options: any = {}) => {
  const createProblemStatement = useCreateLaunchItProblemStatementApi();
  return useMutation(createProblemStatement, {
    mutationKey: ['CREATE_LAUNCH_IT_PROBLEM_STATEMENT'],
    ...options,
  });
};

export const useGenerateProposedSolutions = (options: any = {}) => {
  const generateProposedSolutions = useGenerateProposedSolutionsApi();
  return useMutation<GenerateProposedSolutionsResponse, Error, string>({
    mutationFn: generateProposedSolutions,
    mutationKey: ['GENERATE_PROPOSED_SOLUTIONS'],
    ...options,
  });
};

export const useGenerateAutomationSummary = (options: any = {}) => {
  const generateAutomationSummary = useGenerateAutomationSummaryApi();
  return useMutation<AutomationSummaryResponse, Error, string>({
    mutationFn: generateAutomationSummary,
    mutationKey: ['GENERATE_AUTOMATION_SUMMARY'],
    ...options,
  });
};
