import { useMutation, useQuery } from '@tanstack/react-query';
import {
  useCreateLaunchItProblemStatementApi,
  useGenerateProposedSolutionsApi,
  useGetProposedSolutionApi,
  useGetProposedProjectApi,
  useGetProblemStatementApi,
  GenerateProposedSolutionsResponse,
  LaunchItProblemStatementData,
  ProposedSolution,
  ProposedProject,
} from '../../services/launchItApiRequests';

export const useCreateLaunchItProblemStatement = (options: any = {}) => {
  const createProblemStatement = useCreateLaunchItProblemStatementApi();
  return useMutation(createProblemStatement, {
    mutationKey: ['CREATE_LAUNCH_IT_PROBLEM_STATEMENT'],
    ...options,
  });
};

export const useGenerateProposedSolutions = (options: any = {}) => {
  const generateProposedSolutions = useGenerateProposedSolutionsApi();
  return useMutation<GenerateProposedSolutionsResponse, Error, string>({
    mutationFn: generateProposedSolutions,
    mutationKey: ['GENERATE_PROPOSED_SOLUTIONS'],
    ...options,
  });
};

export const useGetProposedSolution = (
  proposedSolutionRef: string,
  options: any = {},
) => {
  const getProposedSolution = useGetProposedSolutionApi();
  return useQuery<ProposedSolution, Error>({
    queryKey: ['GET_PROPOSED_SOLUTION', proposedSolutionRef],
    queryFn: () => getProposedSolution(proposedSolutionRef),
    enabled: !!proposedSolutionRef,
    ...options,
  });
};

export const useGetProposedProject = (
  proposedProjectRef: string,
  options: any = {},
) => {
  const getProposedProject = useGetProposedProjectApi();
  return useQuery<ProposedProject, Error>({
    queryKey: ['GET_PROPOSED_PROJECT', proposedProjectRef],
    queryFn: () => getProposedProject(proposedProjectRef),
    enabled: !!proposedProjectRef,
    ...options,
  });
};

export const useGetProblemStatement = (
  problemStatementRef: string,
  options: any = {},
) => {
  const getProblemStatement = useGetProblemStatementApi();
  return useQuery<LaunchItProblemStatementData, Error>({
    queryKey: ['GET_PROBLEM_STATEMENT', problemStatementRef],
    queryFn: () => getProblemStatement(problemStatementRef),
    ...options,
  });
};
