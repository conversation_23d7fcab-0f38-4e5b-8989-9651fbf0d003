import { useMutation } from '@tanstack/react-query';
import {
  useCreateLaunchItProblemStatementApi,
  useGenerateProposedSolutionsApi,
} from '../../services/launchItApiRequests';

export const useCreateLaunchItProblemStatement = (options = {}) => {
  const createProblemStatement = useCreateLaunchItProblemStatementApi();
  return useMutation(createProblemStatement, {
    mutationKey: ['CREATE_LAUNCH_IT_PROBLEM_STATEMENT'],
    ...options,
  });
};

export const useGenerateProposedSolutions = (options = {}) => {
  const generateProposedSolutions = useGenerateProposedSolutionsApi();
  return useMutation({
    mutationFn: generateProposedSolutions,
    mutationKey: ['GENERATE_PROPOSED_SOLUTIONS'],
    ...options,
  });
};
