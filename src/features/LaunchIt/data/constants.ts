import { BudgetOption } from '../types';
import orange from '../assets/images/orange.png';
import blue from '../assets/images/blue.png';
import grey from '../assets/images/grey.png';
import brown from '../assets/images/brown.png';
import green from '../assets/images/green.png';

export const overviewCards = [
  {
    header: 'Problem Statement',
    title: 'Start with a Problem',
    description: 'What challenge are you trying to solve?',
    buttonLabel: 'Explore Solutions',
    backgroundImage: orange,
    route: 'problem-statement',
  },
  {
    header: 'Proposed Solution',
    title: 'Start with a Solution/Thesis',
    description: 'What venture do you have in mind?',
    buttonLabel: 'Map It Out',
    backgroundImage: blue,
    // route: 'solution',
  },
  {
    header: 'Transformation',
    title: 'Start with a Strategic Initiative',
    description: 'Which business capabilities do you want to transform?',
    buttonLabel: 'Begin Planning',
    backgroundImage: grey,
    // route: 'transformation',
  },
  {
    header: 'Small Business',
    title: 'Start with a Business template',
    description: 'What small business would you like to start?',
    buttonLabel: 'Start Now',
    backgroundImage: brown,
    // route: 'small-business',
  },
  {
    header: 'Sales Wing',
    title: 'Start with a Sales Campaign',
    description: 'What is your target audience?',
    buttonLabel: 'Begin Campaign',
    backgroundImage: green,
    // route: 'sales',
  },
];

export const problemCards = [
  {
    header: 'Problem Statement',
    title: 'Create problem statement',
    description: '',
    buttonLabel: 'Create',
    backgroundImage: orange,
    route: 'create',
  },
  {
    header: 'Explore a Challenge',
    title: 'Select problem statement',
    description: '',
    buttonLabel: 'Select',
    backgroundImage: grey,
    // route: 'solution',
  },
];

export const budgetOptions: BudgetOption[] = [
  { value: '0-1000', label: '$0 - $1,000' },
  { value: '1000-5000', label: '$1,000 - $5,000' },
  { value: '5000-10000', label: '$5,000 - $10,000' },
  { value: '10000-25000', label: '$10,000 - $25,000' },
  { value: '25000-50000', label: '$25,000 - $50,000' },
  { value: '************', label: '$50,000 - $100,000' },
  { value: '100000+', label: '$100,000+' },
];

export const statusOptions = [
  { value: 'private', label: 'Private' },
  { value: 'public', label: 'Public' },
];

export const mockIndustries = [
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'quality-education', label: 'Quality Education' },
  { value: 'technology', label: 'Technology' },
  { value: 'agriculture', label: 'Agriculture' },
  { value: 'finance', label: 'Finance' },
];

export const mockGoalsCategories = [
  { value: 'sdg-1', label: 'No Poverty' },
  { value: 'sdg-2', label: 'Zero Hunger' },
  { value: 'sdg-3', label: 'Good Health and Well-being' },
  { value: 'sdg-4', label: 'Quality Education' },
  { value: 'sdg-5', label: 'Gender Equality' },
];
