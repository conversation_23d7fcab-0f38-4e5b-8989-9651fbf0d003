import { BudgetOption } from '../types';
import orange from '../assets/images/orange.png';
import blue from '../assets/images/blue.png';
import grey from '../assets/images/grey.png';
import brown from '../assets/images/brown.png';
import green from '../assets/images/green.png';

export const overviewCards = [
  {
    header: 'Problem Statement',
    title: 'Start with a Problem',
    description: 'What challenge are you trying to solve?',
    buttonLabel: 'Explore Solutions',
    backgroundImage: orange,
    route: 'problem-statement',
  },
  {
    header: 'Proposed Solution',
    title: 'Start with a Solution/Thesis',
    description: 'What venture do you have in mind?',
    buttonLabel: 'Map It Out',
    backgroundImage: blue,
    // route: 'solution',
  },
  {
    header: 'Transformation',
    title: 'Start with a Strategic Initiative',
    description: 'Which business capabilities do you want to transform?',
    buttonLabel: 'Begin Planning',
    backgroundImage: grey,
    // route: 'transformation',
  },
  {
    header: 'Small Business',
    title: 'Start with a Business template',
    description: 'What small business would you like to start?',
    buttonLabel: 'Start Now',
    backgroundImage: brown,
    // route: 'small-business',
  },
  {
    header: 'Sales Wing',
    title: 'Start with a Sales Campaign',
    description: 'What is your target audience?',
    buttonLabel: 'Begin Campaign',
    backgroundImage: green,
    // route: 'sales',
  },
];

export const problemCards = [
  {
    header: 'Problem Statement',
    title: 'Create problem statement',
    description: '',
    buttonLabel: 'Create',
    backgroundImage: orange,
    route: 'create',
  },
  {
    header: 'Explore a Challenge',
    title: 'Select problem statement',
    description: '',
    buttonLabel: 'Select',
    backgroundImage: grey,
    // route: 'dashboard-statements',
  },
];

export const budgetOptions: BudgetOption[] = [
  { value: '0-1000', label: '$0 - $1,000' },
  { value: '1000-5000', label: '$1,000 - $5,000' },
  { value: '5000-10000', label: '$5,000 - $10,000' },
  { value: '10000-25000', label: '$10,000 - $25,000' },
  { value: '25000-50000', label: '$25,000 - $50,000' },
  { value: '************', label: '$50,000 - $100,000' },
  { value: '100000+', label: '$100,000+' },
];

export const statusOptions = [
  { value: 'private', label: 'Private' },
  { value: 'public', label: 'Public' },
];

export const mockIndustries = [
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'quality-education', label: 'Quality Education' },
  { value: 'technology', label: 'Technology' },
  { value: 'agriculture', label: 'Agriculture' },
  { value: 'finance', label: 'Finance' },
];

export const sdgGoalsCategories = [
  {
    categoryRef: 'CTG_NIHXBHKI94C3',
    categoryName: 'Affordable and Clean Energy',
    subcategories: [
      {
        subcategoryRef: 'SBC_FXU79U9WYEVF',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_TQLWFA5OZI1A',
    categoryName: 'Climate Action',
    subcategories: [
      {
        subcategoryRef: 'SBC_WWGU7WBNQSVC',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_RTHVXX3CQLUL',
    categoryName: 'Decent Work and Economic Growth',
    subcategories: [
      {
        subcategoryRef: 'SBC_JEWPZ7AWDXMH',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_3B6DVLA7HDFG',
    categoryName: 'Good Governance',
    subcategories: [
      {
        subcategoryRef: 'SBC_D18UICFFLYBC',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_JB4ZHMPSCMJO',
    categoryName: 'Good Health and Well-Being',
    subcategories: [
      {
        subcategoryRef: 'SBC_H4PUBMVPXMWO',
        subcategoryName: 'Health Information Technology (HIT) Solutions',
      },
      {
        subcategoryRef: 'SBC_KQH0HF4SSHID',
        subcategoryName: 'Telehealth and Remote Care',
      },
      {
        subcategoryRef: 'SBC_ZNFXTRQMBPTH',
        subcategoryName: 'Patient Engagement And Experience',
      },
      {
        subcategoryRef: 'SBC_IVQCMXAYJTI3',
        subcategoryName: 'Healthcare Finance And Payment',
      },
      {
        subcategoryRef: 'SBC_MDEO0ADYM7EA',
        subcategoryName: 'Medical Devices And Equipment Management',
      },
      {
        subcategoryRef: 'SBC_BAU6BF3E1OMF',
        subcategoryName: 'Healthcare Logistics And Supply Chain',
      },
      {
        subcategoryRef: 'SBC_UBWFPTDLQD8M',
        subcategoryName: 'Healthcare Security And Compliance',
      },
      {
        subcategoryRef: 'SBC_OQTEOZ9PEKCY',
        subcategoryName: 'Chronic Disease Management',
      },
      {
        subcategoryRef: 'SBC_BTQHPABGZJ92',
        subcategoryName: 'Population Health Management',
      },
      {
        subcategoryRef: 'SBC_M4H3ULNZMDKF',
        subcategoryName: 'Healthcare Policy And Regulation Technology',
      },
      {
        subcategoryRef: 'SBC_PPDSVPJCMAPM',
        subcategoryName: 'Mental Health And Wellness Solutions',
      },
      {
        subcategoryRef: 'SBC_OEQPG2G28G6S',
        subcategoryName: 'Healthcare Workforce And Staffing Innovations',
      },
      {
        subcategoryRef: 'SBC_RR6VF7YLNPJB',
        subcategoryName: 'Drug Discovery And Pharmaceutical Innovation',
      },
      {
        subcategoryRef: 'SBC_0WLIIWJOPZBD',
        subcategoryName: 'Personalized Medicine And Genomics',
      },
      {
        subcategoryRef: 'SBC_WEHFRHVC9AQJ',
        subcategoryName: 'Healthcare Quality And Performance',
      },
      {
        subcategoryRef: 'SBC_W88AFVNFAZAN',
        subcategoryName:
          'Health-Tech Solutions For Seniors And Aging Populations',
      },
      {
        subcategoryRef: 'SBC_OE6ZYFH58UVR',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_LKGZDC9JLLZ6',
    categoryName: 'Industry, Innovation, Infrastructure and Trade',
    subcategories: [
      {
        subcategoryRef: 'SBC_C65PJ5DD5RFS',
        subcategoryName: 'Customs And Regulatory Compliance Solutions',
      },
      {
        subcategoryRef: 'SBC_KJCJTP2RG4YQ',
        subcategoryName: 'Global Logistics Optimization',
      },
      {
        subcategoryRef: 'SBC_FIQRXH0NSAS1',
        subcategoryName: 'Cross-Border E-Commerce Platforms',
      },
      {
        subcategoryRef: 'SBC_QLFSUJ7EKJER',
        subcategoryName: 'Traceability And Transparency',
      },
      {
        subcategoryRef: 'SBC_11NO8IIR09WE',
        subcategoryName: 'Blockchain For Supply Chain',
      },
      {
        subcategoryRef: 'SBC_RJXBP08YOJXF',
        subcategoryName: 'Market Access And Expansion Tools',
      },
      {
        subcategoryRef: 'SBC_QKPGSNT3PBGZ',
        subcategoryName: 'Cross-Cultural Communication Solutions',
      },
      {
        subcategoryRef: 'SBC_I7XKDS5AAY6C',
        subcategoryName: 'Global Trade Analytics',
      },
      {
        subcategoryRef: 'SBC_1JKJ1XSKBNXW',
        subcategoryName: 'Borderless Payment Solutions',
      },
      {
        subcategoryRef: 'SBC_XHHQ7LAO7ZCZ',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_0U5SMAFZGSPM',
    categoryName: 'No Poverty',
    subcategories: [],
  },
  {
    categoryRef: 'CTG_CRW6Q4LLUPNX',
    categoryName: 'Peace, Justice and Strong Institutions',
    subcategories: [
      {
        subcategoryRef: 'SBC_8CEJH9BVVVCG',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_ZA6QERX1SXM1',
    categoryName: 'Quality Education',
    subcategories: [
      {
        subcategoryRef: 'SBC_WD3P0JVJFDHF',
        subcategoryName: 'Online Learning Platforms',
      },
      {
        subcategoryRef: 'SBC_XXXVIEJILVPI',
        subcategoryName: 'Skill-Specific Training',
      },
      {
        subcategoryRef: 'SBC_BC8MXGSBW8AU',
        subcategoryName: 'EdTech for Schools',
      },
      {
        subcategoryRef: 'SBC_Q8DIX8WCYCQZ',
        subcategoryName: 'Career Development Services',
      },
      {
        subcategoryRef: 'SBC_IGEYBDC4O7YJ',
        subcategoryName: 'Gamified Learning',
      },
      {
        subcategoryRef: 'SBC_SRW2WTXBO4ZY',
        subcategoryName: 'Language Learning Innovations',
      },
      {
        subcategoryRef: 'SBC_EKLETD8YXNYJ',
        subcategoryName: 'Professional Development',
      },
      {
        subcategoryRef: 'SBC_Z7QE0VVZL0CX',
        subcategoryName: 'AI In Education',
      },
      {
        subcategoryRef: 'SBC_DXGL8PPCLFUL',
        subcategoryName: 'Innovations In Assessments',
      },
      {
        subcategoryRef: 'SBC_NW3ZDAQSKK2U',
        subcategoryName: 'Soft Skills Training',
      },
      {
        subcategoryRef: 'SBC_4LSXKX0VSW8K',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_5SNZADG5YX04',
    categoryName: 'Zero Hunger and Food Security',
    subcategories: [
      {
        subcategoryRef: 'SBC_9MNNE7H4QQ5F',
        subcategoryName: 'Smart Logistics And Distribution',
      },
      {
        subcategoryRef: 'SBC_EIV1YAL57HUY',
        subcategoryName: 'Precision Agriculture',
      },
      {
        subcategoryRef: 'SBC_6IQ2SELDNP3L',
        subcategoryName: 'Cold Chain Technology',
      },
      {
        subcategoryRef: 'SBC_X1JVSJIZI6TH',
        subcategoryName: 'Inventory Management',
      },
      {
        subcategoryRef: 'SBC_DTKDT4S19DLU',
        subcategoryName: 'Traceability And Transparency',
      },
      {
        subcategoryRef: 'SBC_YV3IPXYFXCUR',
        subcategoryName: 'Sustainable Packaging',
      },
      {
        subcategoryRef: 'SBC_ZTRME3NNBZVW',
        subcategoryName: 'Quality Assurance And Compliance',
      },
      {
        subcategoryRef: 'SBC_HLCOIM60HDN5',
        subcategoryName: 'Blockchain In Supply Chain',
      },
      {
        subcategoryRef: 'SBC_ZDX8JMQ0EJT1',
        subcategoryName: 'Marketplace And Direct-to-Consumer Platforms',
      },
      {
        subcategoryRef: 'SBC_CUAS7BMAUIWC',
        subcategoryName: 'Waste Reduction And Recycling',
      },
      {
        subcategoryRef: 'SBC_SPRIUWCHXASZ',
        subcategoryName: 'Other',
      },
    ],
  },
  {
    categoryRef: 'CTG_BF64RWTW1ERW',
    categoryName: 'Other',
    subcategories: [
      {
        subcategoryRef: 'SBC_Z7ENUOQQD8NT',
        subcategoryName: 'Other',
      },
    ],
  },
];
