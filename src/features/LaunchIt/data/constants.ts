import { BudgetOption } from '../types';

export const budgetOptions: BudgetOption[] = [
  { value: '0-1000', label: '$0 - $1,000' },
  { value: '1000-5000', label: '$1,000 - $5,000' },
  { value: '5000-10000', label: '$5,000 - $10,000' },
  { value: '10000-25000', label: '$10,000 - $25,000' },
  { value: '25000-50000', label: '$25,000 - $50,000' },
  { value: '50000-100000', label: '$50,000 - $100,000' },
  { value: '100000+', label: '$100,000+' },
];

export const statusOptions = [
  { value: 'private', label: 'Private' },
  { value: 'public', label: 'Public' },
];

export const mockIndustries = [
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'quality-education', label: 'Quality Education' },
  { value: 'technology', label: 'Technology' },
  { value: 'agriculture', label: 'Agriculture' },
  { value: 'finance', label: 'Finance' },
];

export const mockGoalsCategories = [
  { value: 'sdg-1', label: 'No Poverty' },
  { value: 'sdg-2', label: 'Zero Hunger' },
  { value: 'sdg-3', label: 'Good Health and Well-being' },
  { value: 'sdg-4', label: 'Quality Education' },
  { value: 'sdg-5', label: 'Gender Equality' },
];
