import orange from '../assets/images/orange.png';
import blue from '../assets/images/blue.png';
import grey from '../assets/images/grey.png';
import brown from '../assets/images/brown.png';
import green from '../assets/images/green.png';

export const overviewCards = [
  {
    header: 'Problem Statement',
    title: 'Start with a Problem',
    description: 'What challenge are you trying to solve?',
    buttonLabel: 'Explore Solutions',
    backgroundImage: orange,
    route: 'problem-statement',
  },
  {
    header: 'Proposed Solution',
    title: 'Start with a Solution/Thesis',
    description: 'What venture do you have in mind?',
    buttonLabel: 'Map It Out',
    backgroundImage: blue,
    // route: 'solution',
  },
  {
    header: 'Transformation',
    title: 'Start with a Strategic Initiative',
    description: 'Which business capabilities do you want to transform?',
    buttonLabel: 'Begin Planning',
    backgroundImage: grey,
    // route: 'transformation',
  },
  {
    header: 'Small Business',
    title: 'Start with a Business template',
    description: 'What small business would you like to start?',
    buttonLabel: 'Start Now',
    backgroundImage: brown,
    // route: 'small-business',
  },
  {
    header: 'Sales Wing',
    title: 'Start with a Sales Campaign',
    description: 'What is your target audience?',
    buttonLabel: 'Begin Campaign',
    backgroundImage: green,
    // route: 'sales',
  },
];

export const problemCards = [
  {
    header: 'Problem Statement',
    title: 'Create problem statement',
    description: '',
    buttonLabel: 'Create',
    backgroundImage: orange,
    route: 'create',
  },
  {
    header: 'Explore a Challenge',
    title: 'Select problem statement',
    description: '',
    buttonLabel: 'Select',
    backgroundImage: grey,
    // route: 'solution',
  },
];
