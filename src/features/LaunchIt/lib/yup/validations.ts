import * as yup from 'yup';

export const createProblemStatementSchema = yup
  .object()
  .shape({
    description: yup
      .string()
      .required('Description is a required field')
      .max(5000, 'Description cannot exceed 5000 characters'),
    detailedDescriptionFile: yup.mixed<File>().nullable(),
    focusCountries: yup
      .array()
      .of(yup.string().required('Country is a required field'))
      .min(1, 'Please select at least one focus country')
      .max(5, 'You can select a maximum of 5 focus countries')
      .required('Focus countries is a required field'),
    budget: yup.string().required('Budget is a required field'),
    status: yup
      .string()
      .oneOf(['private', 'public'], 'Status must be either private or public')
      .required('Status is a required field'),
    videoLink: yup
      .string()
      .url('Please enter a valid YouTube URL')
      .required('Video link is a required field'),
  })
  .required();
