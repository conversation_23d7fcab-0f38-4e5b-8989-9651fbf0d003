import { useAppContext } from '@/context/event/AppEventContext';
import { cn } from '@/lib/twMerge/cn';
import { SetStateAction, useEffect, useState, useRef } from 'react';
import { ConversationHelper } from '../../utils/helper';
import { file } from '@/types';
import { useConversationContext } from '../../context/conversation/conversationContext';
import { useNavigate } from 'react-router-dom';
import { Play, Pause } from 'lucide-react';

export const MediaPreviewComponent = ({
  feedTopicId,
  files,
}: {
  files: file[];
  feedTopicId: string;
}) => {
  const [visibleFiles, setVisibleFiles] = useState<{ [key: string]: boolean }>(
    {},
  );

  const isAllFilesVisible =
    files.length > 4 &&
    Object.values(visibleFiles).length >= 4 &&
    Object.values(visibleFiles).every(Boolean);

  return (
    <div
      className={cn(
        'mx-auto grid h-full max-h-[400px] max-w-full cursor-pointer grid-cols-[1fr_minmax(33.33%,1fr)] gap-1',
        files.length <= 2
          ? 'grid-rows-1'
          : files.length === 3
            ? 'grid-rows-2'
            : 'grid-rows-3',
      )}
    >
      {files?.slice(0, 4).map((file, index) => (
        <MediaComponent
          key={file?.fileUrl || index}
          feedTopicId={feedTopicId}
          file={file}
          index={index}
          files={files}
          setVisibleFiles={setVisibleFiles}
          isAllFilesVisible={isAllFilesVisible}
        />
      ))}
    </div>
  );
};

const MediaComponent = ({
  file,
  index,
  feedTopicId,
  files,
  setVisibleFiles,
  isAllFilesVisible,
}: {
  setVisibleFiles: (value: SetStateAction<{ [key: string]: boolean }>) => void;
  files: file[];
  index: number;
  file: file;
  feedTopicId: string;
  isAllFilesVisible: boolean;
}) => {
  const navigate = useNavigate();
  const { setShowModalHandler } = useAppContext();
  const { setCurrentSlideHandler, setTopicIdHandler } =
    useConversationContext();
  const [loaded, setLoaded] = useState(false);
  const [imageSrc, setImageSrc] = useState(file.thumbnail);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [shouldLoadVideo, setShouldLoadVideo] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const isMediaPage = window.location.pathname.includes('/media');

  // Preload high-quality image
  useEffect(() => {
    const img = new Image();
    img.src = file.fileUrl;
    img.onload = () => {
      setImageSrc(file.fileUrl);
      setLoaded(true);
      setVisibleFiles(prev => ({ ...prev, [index]: true }));
    };
    img.onerror = () => {
      setVisibleFiles(prev => ({ ...prev, [index]: false }));
    };
  }, [file.fileUrl, index, setVisibleFiles]);

  // Intersection Observer for lazy video loading
  useEffect(() => {
    if (!ConversationHelper.isVideo(file?.fileUrl || '')) return;
    const node = containerRef.current;
    if (!node) return;
    let observer: IntersectionObserver | null = null;
    if ('IntersectionObserver' in window) {
      observer = new window.IntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              setShouldLoadVideo(true);
              observer && observer.disconnect();
            }
          });
        },
        { threshold: 0.25 },
      );
      observer.observe(node);
    } else {
      // Fallback: load immediately if no IntersectionObserver
      setShouldLoadVideo(true);
    }
    return () => {
      observer && observer.disconnect();
    };
  }, [file?.fileUrl]);

  const handleVideoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!videoRef.current) return;

    if (isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
    } else {
      videoRef.current.play();
      setIsVideoPlaying(true);
    }
  };

  const handleVideoEnded = () => {
    setIsVideoPlaying(false);
  };

  const handleContainerClick = () => {
    if (isMediaPage) return navigate(`/media/t/${feedTopicId}`);

    setShowModalHandler('TopicModal');
    setTopicIdHandler(feedTopicId);
    setCurrentSlideHandler(index);
  };

  return (
    <div
      ref={containerRef}
      onClick={handleContainerClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        'relative',
        index === 0 && 'max-h-auto row-[1/4]',
        index === 0 && files.length === 1 && 'col-[1/3]',
        index === 0 && files.length > 1 && 'col-[1]',
      )}
    >
      {ConversationHelper.isImage(file?.fileUrl || '') && (
        <>
          {!loaded ? (
            <div className="h-full w-full animate-pulse bg-gray-200" />
          ) : (
            <img
              src={imageSrc}
              loading="lazy"
              alt="Content preview"
              className={`h-full max-h-[400px] w-full max-w-full border-2 border-white object-cover transition duration-100`}
            />
          )}
        </>
      )}

      {ConversationHelper.isVideo(file?.fileUrl || '') && (
        <div className="relative h-full max-h-[400px] w-full max-w-full">
          <video
            ref={videoRef}
            muted
            autoPlay={false}
            playsInline
            controls={false}
            preload="none"
            poster={file.thumbnail}
            src={shouldLoadVideo ? file.fileUrl : undefined}
            onEnded={handleVideoEnded}
            onLoadedData={() => setVideoLoaded(true)}
            className="h-full w-full object-cover"
          />

          {/* Play button overlay: only show after videoLoaded and not playing */}
          {!isVideoPlaying && shouldLoadVideo && (
            <button
              type="button"
              onClick={handleVideoClick}
              className="absolute inset-0 flex scale-[0.9] items-center justify-center rounded-2xl transition-all duration-200 ease-out group-hover:scale-100"
            >
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 backdrop-blur-md">
                <div className="relative flex h-12 w-12 scale-100 items-center justify-center rounded-full bg-gradient-to-b from-primary/30 to-primary shadow-md transition-all duration-200 ease-out group-hover:scale-[1.2]">
                  <Play
                    className="h-5 w-5 scale-100 fill-white text-white transition-transform duration-200 ease-out group-hover:scale-105"
                    style={{
                      filter:
                        'drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06))',
                    }}
                  />
                </div>
              </div>
            </button>
          )}

          {/* Pause button overlay: only show after videoLoaded, when playing, and on hover */}
          {isVideoPlaying && videoLoaded && isHovered && (
            <div
              onClick={handleVideoClick}
              className="absolute inset-0 z-10 flex cursor-pointer items-center justify-center"
            >
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 backdrop-blur-md">
                <div className="relative flex h-12 w-12 scale-100 items-center justify-center rounded-full bg-gradient-to-b from-primary/30 to-primary shadow-md">
                  <Pause
                    className="h-5 w-5 scale-100 fill-white text-white"
                    style={{
                      filter:
                        'drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06))',
                    }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {files?.length - 4 > 0 && index === 3 && isAllFilesVisible && (
        <div className="absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center bg-black/50">
          <span className="text-3xl font-medium text-white sm:text-6xl">
            +{files.length - 4}
          </span>
        </div>
      )}
    </div>
  );
};
